package org.dromara.common.tlcache.exception;

import lombok.Getter;

/**
 * TlCache缓存异常基类
 * <p>
 * 用于封装缓存操作过程中的各种异常情况，
 * 提供更具体的异常信息和错误处理机制
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Getter
public class TlCacheException extends RuntimeException {

    /**
     * 错误码
     * -- GETTER --
     * 获取错误码
     *
     * @return 错误码
     */
    private final String errorCode;

    /**
     * 构造函数
     *
     * @param message 异常信息
     */
    public TlCacheException(String message) {
        super(message);
        this.errorCode = "CACHE_ERROR";
    }

    /**
     * 构造函数
     *
     * @param message 异常信息
     * @param cause   原始异常
     */
    public TlCacheException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "CACHE_ERROR";
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误码
     * @param message   异常信息
     */
    public TlCacheException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误码
     * @param message   异常信息
     * @param cause     原始异常
     */
    public TlCacheException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

}
