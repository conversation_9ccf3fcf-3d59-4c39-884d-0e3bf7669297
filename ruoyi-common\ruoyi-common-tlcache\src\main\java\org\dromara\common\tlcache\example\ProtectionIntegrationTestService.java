package org.dromara.common.tlcache.example;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.tlcache.annotation.BloomFilter;
import org.dromara.common.tlcache.annotation.RandomExpire;
import org.dromara.common.tlcache.annotation.TlCache;
import org.dromara.common.tlcache.enums.RandomStrategy;
import org.springframework.stereotype.Service;

/**
 * 防护机制整合测试服务
 * <p>
 * 用于测试切面中整合的BloomFilterManager和RandomExpireManager是否正常工作
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Slf4j
public class ProtectionIntegrationTestService {

    /**
     * 测试布隆过滤器防穿透
     * <p>
     * 当查询不存在的用户时，布隆过滤器应该拦截请求
     */
    @TlCache(
        cacheName = "testUser",
        key = "#userId",
        l1ExpireSeconds = 300,
        l2ExpireSeconds = 1800,
        bloomFilter = @BloomFilter(
            enabled = true,
            expectedInsertions = 10000,
            fpp = 0.01,
            autoInit = true
        )
    )
    public TestUser getTestUser(Long userId) {
        log.info("从数据库查询测试用户: {}", userId);

        // 模拟数据库查询逻辑
        if (userId <= 0 || userId > 1000) {
            log.info("用户不存在: {}", userId);
            return null; // 用户不存在
        }

        log.info("找到用户: {}", userId);
        return new TestUser(userId, "测试用户" + userId, "test" + userId + "@example.com");
    }

    /**
     * 测试随机过期防雪崩
     * <p>
     * 缓存过期时间应该在基础时间上增加随机时间
     */
    @TlCache(
        cacheName = "testProduct",
        key = "#productId",
        l1ExpireSeconds = 300,   // 基础5分钟
        l2ExpireSeconds = 1800,  // 基础30分钟
        randomExpire = @RandomExpire(
            enabled = true,
            minSeconds = 0,
            maxSeconds = 300,    // 随机增加0-5分钟
            strategy = RandomStrategy.UNIFORM,
            applyToL1 = true     // 对L1和L2都应用随机过期
        )
    )
    public TestProduct getTestProduct(Long productId) {
        log.info("从数据库查询测试商品: {}", productId);

        // 模拟数据库查询
        return new TestProduct(productId, "测试商品" + productId, 99.99 + productId);
    }

    /**
     * 测试综合防护机制
     * <p>
     * 同时启用布隆过滤器和随机过期
     */
    @TlCache(
        cacheName = "testData",
        key = "#dataId",
        l1ExpireSeconds = 600,
        l2ExpireSeconds = 3600,
        bloomFilter = @BloomFilter(
            enabled = true,
            expectedInsertions = 50000,
            fpp = 0.005,
            autoInit = true
        ),
        randomExpire = @RandomExpire(
            enabled = true,
            minSeconds = 0,
            maxSeconds = 600,
            strategy = RandomStrategy.NORMAL,
            applyToL1 = false
        )
    )
    public TestData getTestData(String dataId) {
        log.info("从数据库查询测试数据: {}", dataId);

        // 模拟数据查询逻辑
        if (dataId == null || dataId.startsWith("invalid")) {
            log.info("数据不存在: {}", dataId);
            return null;
        }

        return new TestData(dataId, "测试数据" + dataId, System.currentTimeMillis());
    }

    /**
     * 测试无防护机制的普通缓存
     * <p>
     * 作为对比，验证防护机制不会影响正常的缓存功能
     */
    @TlCache(
        cacheName = "normalData",
        key = "#id",
        l1ExpireSeconds = 300,
        l2ExpireSeconds = 1800
    )
    public NormalData getNormalData(Long id) {
        log.info("从数据库查询普通数据: {}", id);
        return new NormalData(id, "普通数据" + id);
    }

    /**
     * 批量测试方法，用于验证防护机制的效果
     */
    public void runProtectionTests() {
        log.info("开始运行防护机制测试...");

        // 测试布隆过滤器
        log.info("=== 测试布隆过滤器防穿透 ===");

        // 先查询存在的用户，应该正常返回并添加到布隆过滤器
        TestUser user1 = getTestUser(1L);
        log.info("查询存在用户结果: {}", user1);

        TestUser user2 = getTestUser(500L);
        log.info("查询存在用户结果: {}", user2);

        // 再次查询相同用户，应该从缓存返回
        TestUser user1Again = getTestUser(1L);
        log.info("再次查询用户结果: {}", user1Again);

        // 查询不存在的用户，布隆过滤器可能会拦截
        TestUser invalidUser = getTestUser(9999L);
        log.info("查询不存在用户结果: {}", invalidUser);

        // 测试随机过期
        log.info("=== 测试随机过期防雪崩 ===");

        TestProduct product1 = getTestProduct(1L);
        log.info("查询商品结果: {}", product1);

        TestProduct product2 = getTestProduct(2L);
        log.info("查询商品结果: {}", product2);

        // 测试综合防护
        log.info("=== 测试综合防护机制 ===");

        TestData data1 = getTestData("valid-data-1");
        log.info("查询有效数据结果: {}", data1);

        TestData invalidData = getTestData("invalid-data");
        log.info("查询无效数据结果: {}", invalidData);

        // 测试普通缓存
        log.info("=== 测试普通缓存 ===");

        NormalData normal1 = getNormalData(1L);
        log.info("查询普通数据结果: {}", normal1);

        log.info("防护机制测试完成！");
    }

    // 测试数据类
    public record TestUser(Long userId, String userName, String email) {
    }

    public record TestProduct(Long productId, String productName, Double price) {
    }

    public record TestData(String dataId, String dataName, Long timestamp) {
    }

    public record NormalData(Long id, String name) {
    }
}
