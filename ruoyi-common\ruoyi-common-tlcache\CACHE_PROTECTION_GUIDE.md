# 🛡️ TlCache 缓存防护机制使用指南

## 📋 概述

TlCache 提供了缓存防护机制，有效防止缓存穿透和缓存雪崩等问题。

### 🎯 防护机制总览

| 防护类型 | 解决问题 | 实现方式 | 性能影响 | 推荐场景 |
|----------|----------|----------|----------|----------|
| **布隆过滤器** | 缓存穿透 | Redis布隆过滤器 | 极低 | 大量无效查询 |
| **随机过期** | 缓存雪崩 | 随机过期时间 | 无 | 集中过期风险 |

---

## 🔍 布隆过滤器防穿透

### 原理说明
布隆过滤器可以高效判断一个元素是否**可能存在**于集合中，用于防止查询不存在的数据导致的缓存穿透。

### 基础用法
```java
@TlCache(
    cacheName = "userInfo",
    key = "#userId",
    bloomFilter = @BloomFilter(
        enabled = true,
        expectedInsertions = 1000000,  // 预期100万用户
        fpp = 0.01                     // 1%误判率
    )
)
public UserInfo getUserInfo(Long userId) {
    // 只有布隆过滤器认为可能存在的用户才会查询数据库
    return userMapper.selectById(userId);
}
```

### 高级配置
```java
@BloomFilter(
    enabled = true,
    expectedInsertions = 5000000,      // 预期500万数据
    fpp = 0.001,                       // 0.1%误判率（更精确）
    filterName = "global_user_filter", // 自定义过滤器名称
    autoInit = true,                   // 自动初始化
    expireSeconds = 86400,             // 24小时过期重建
    enableWarmup = true,               // 启用预热
    warmupBatchSize = 5000             // 预热批量大小
)
```

### 配置建议

| 场景 | expectedInsertions | fpp | 说明 |
|------|-------------------|-----|------|
| **小型应用** | 100,000 | 0.01 | 平衡性能和内存 |
| **中型应用** | 1,000,000 | 0.01 | 标准配置 |
| **大型应用** | 10,000,000 | 0.005 | 高精度配置 |
| **超大型应用** | 100,000,000 | 0.001 | 极高精度配置 |

---

## ❄️ 随机过期防雪崩

### 原理说明
通过在基础过期时间上增加随机时间，避免大量缓存同时过期导致的缓存雪崩。

### 基础用法
```java
@TlCache(
    cacheName = "productInfo",
    key = "#productId",
    l2ExpireSeconds = 3600,            // 基础1小时过期
    randomExpire = @RandomExpire(
        enabled = true,
        minSeconds = 0,
        maxSeconds = 600               // 随机增加0-10分钟
    )
)
public ProductInfo getProductInfo(Long productId) {
    // 实际过期时间：3600 + random(0, 600) 秒
    return productMapper.selectById(productId);
}
```

### 随机策略对比
```java
// 1. 均匀分布（默认）
@RandomExpire(
    strategy = RandomExpireConfig.RandomStrategy.UNIFORM,
    minSeconds = 0,
    maxSeconds = 300
)

// 2. 正态分布（中心集中）
@RandomExpire(
    strategy = RandomExpireConfig.RandomStrategy.NORMAL,
    minSeconds = 0,
    maxSeconds = 300
)

// 3. 指数分布（偏向较小值）
@RandomExpire(
    strategy = RandomExpireConfig.RandomStrategy.EXPONENTIAL,
    minSeconds = 0,
    maxSeconds = 300
)
```

### 配置建议

| 基础过期时间 | 推荐随机范围 | 说明 |
|-------------|-------------|------|
| **< 5分钟** | 10-20% | 短期缓存，小幅随机 |
| **5-60分钟** | 15-25% | 中期缓存，适度随机 |
| **> 1小时** | 20-30% | 长期缓存，较大随机 |



---

## 🔧 综合配置示例

### 高安全性配置
```java
@TlCache(
    cacheName = "sensitiveData",
    key = "#dataId",
    l1ExpireSeconds = 300,
    l2ExpireSeconds = 1800,
    // 布隆过滤器防穿透
    bloomFilter = @BloomFilter(
        enabled = true,
        expectedInsertions = 1000000,
        fpp = 0.001                    // 低误判率
    ),
    // 随机过期防雪崩
    randomExpire = @RandomExpire(
        enabled = true,
        maxSeconds = 300,
        strategy = RandomExpireConfig.RandomStrategy.NORMAL
    )
)
public SensitiveData getSensitiveData(String dataId) {
    return sensitiveDataMapper.selectById(dataId);
}
```

### 高性能配置
```java
@TlCache(
    cacheName = "highPerformanceData",
    key = "#dataId",
    l1ExpireSeconds = 600,
    l2ExpireSeconds = 3600,
    // 高性能布隆过滤器
    bloomFilter = @BloomFilter(
        enabled = true,
        expectedInsertions = 5000000,
        fpp = 0.05                     // 较高误判率，更小内存
    ),
    // 适度随机过期
    randomExpire = @RandomExpire(
        enabled = true,
        maxSeconds = 180
    )
)
public HighPerformanceData getHighPerformanceData(String dataId) {
    return dataMapper.selectById(dataId);
}
```

---

## 📊 监控和调优

### 性能监控指标
- **布隆过滤器命中率**：监控拦截效果
- **随机过期分布**：确保过期时间分散

### 调优建议
1. **根据业务特点选择合适的防护策略**
2. **定期分析监控数据，调整配置参数**
3. **在测试环境验证配置效果**
4. **逐步启用防护机制，避免影响正常业务**

---

## ⚠️ 注意事项

1. **布隆过滤器**：存在误判，需要合理设置误判率
2. **随机过期**：会增加缓存的不确定性

建议在生产环境使用前充分测试防护机制的效果和影响。
