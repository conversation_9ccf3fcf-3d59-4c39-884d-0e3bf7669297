package org.dromara.common.tlcache.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * SpEL表达式缓存工具类
 * <p>
 * 缓存已解析的SpEL表达式，提高性能并避免重复解析
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Slf4j
public final class SpelExpressionCache {

    private static final ExpressionParser PARSER = new SpelExpressionParser();

    /**
     * 表达式缓存
     * 键为表达式字符串，值为解析后的Expression对象
     */
    private static final Map<String, Expression> EXPRESSION_CACHE = new ConcurrentHashMap<>();

    /**
     * 缓存大小限制，防止内存泄漏
     */
    private static final int MAX_CACHE_SIZE = 1000;

    private SpelExpressionCache() {
        // 工具类，禁止实例化
    }

    /**
     * 获取或解析SpEL表达式
     * <p>
     * 首先从缓存中查找，如果不存在则解析并缓存
     *
     * @param expressionString 表达式字符串
     * @return 解析后的Expression对象
     * @throws IllegalArgumentException 表达式解析失败时抛出
     */
    public static Expression getOrParseExpression(String expressionString) {
        if (expressionString == null || expressionString.trim().isEmpty()) {
            throw new IllegalArgumentException("表达式字符串不能为空");
        }

        return EXPRESSION_CACHE.computeIfAbsent(expressionString, key -> {
            try {
                // 检查缓存大小，防止内存泄漏
                if (EXPRESSION_CACHE.size() >= MAX_CACHE_SIZE) {
                    log.warn("SpEL表达式缓存已达到最大限制: {}, 清理部分缓存", MAX_CACHE_SIZE);
                    clearOldestEntries();
                }

                Expression expression = PARSER.parseExpression(key);
                log.debug("解析并缓存SpEL表达式: {}", key);
                return expression;
            } catch (Exception e) {
                log.error("解析SpEL表达式失败: {}", key, e);
                throw new IllegalArgumentException("SpEL表达式解析失败: " + key, e);
            }
        });
    }

    /**
     * 清理缓存中的部分条目
     * <p>
     * 当缓存达到最大限制时，清理一部分条目以释放内存
     */
    private static void clearOldestEntries() {
        // 简单策略：清理一半的缓存
        int targetSize = MAX_CACHE_SIZE / 2;
        int currentSize = EXPRESSION_CACHE.size();

        if (currentSize > targetSize) {
            EXPRESSION_CACHE.entrySet().removeIf(entry -> EXPRESSION_CACHE.size() > targetSize);
            log.info("清理SpEL表达式缓存完成，当前大小: {}", EXPRESSION_CACHE.size());
        }
    }

    /**
     * 获取缓存大小
     *
     * @return 当前缓存中的表达式数量
     */
    public static int getCacheSize() {
        return EXPRESSION_CACHE.size();
    }

    /**
     * 清空所有缓存
     * <p>
     * 主要用于测试或特殊情况下的缓存重置
     */
    public static void clearAll() {
        EXPRESSION_CACHE.clear();
        log.info("清空所有SpEL表达式缓存");
    }

    /**
     * 检查表达式是否已缓存
     *
     * @param expressionString 表达式字符串
     * @return 如果已缓存返回true，否则返回false
     */
    public static boolean isCached(String expressionString) {
        return EXPRESSION_CACHE.containsKey(expressionString);
    }
}
