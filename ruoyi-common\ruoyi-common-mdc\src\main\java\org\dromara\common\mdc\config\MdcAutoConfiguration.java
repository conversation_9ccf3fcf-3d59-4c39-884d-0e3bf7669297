package org.dromara.common.mdc.config;

import org.dromara.common.mdc.filter.RequestIdFilter;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;

/**
 * 短信配置类
 *
 * <AUTHOR>
 */
@AutoConfiguration
public class MdcAutoConfiguration {

    @Bean
    public FilterRegistrationBean<RequestIdFilter> requestIdFilterRegistration(RequestIdFilter requestIdFilter) {
        FilterRegistrationBean<RequestIdFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(requestIdFilter);
        // 确保是最先执行的过滤器之一
        registration.setOrder(1);
        registration.addUrlPatterns("/*");
        return registration;
    }
}
