package org.dromara.common.tlcache.example;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.tlcache.annotation.BloomFilter;
import org.dromara.common.tlcache.annotation.TlCache;
import org.springframework.stereotype.Service;

/**
 * 布隆过滤器预热功能使用示例
 * <p>
 * 展示如何使用配置化的布隆过滤器预热功能，
 * 配合@TlCache注解实现缓存防穿透
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
public class BloomFilterWarmupExampleService {

    /**
     * 用户信息查询示例
     * <p>
     * 使用配置化预热的用户布隆过滤器防止缓存穿透
     * 对应配置文件中name为"user"的预热配置
     */
    @TlCache(
        cacheName = "user",
        key = "#userId",
        l1ExpireSeconds = 300,
        l2ExpireSeconds = 1800,
        bloomFilter = @BloomFilter(
            enabled = true,
            filterName = "user",           // 对应配置中的filter-name
            expectedInsertions = 500000,   // 与配置保持一致
            fpp = 0.005                    // 与配置保持一致
        )
    )
    public UserInfo getUserInfo(Long userId) {
        log.info("从数据库查询用户信息: {}", userId);

        // 模拟数据库查询
        if (userId <= 0 || userId > 1000000) {
            // 用户不存在
            return null;
        }

        return new UserInfo(userId, "用户" + userId, "user" + userId + "@example.com");
    }

    /**
     * 角色信息查询示例
     * <p>
     * 使用配置化预热的角色布隆过滤器防止缓存穿透
     * 对应配置文件中name为"role"的预热配置
     */
    @TlCache(
        cacheName = "role",
        key = "#roleId",
        l1ExpireSeconds = 600,
        l2ExpireSeconds = 3600,
        bloomFilter = @BloomFilter(
            enabled = true,
            filterName = "role",           // 对应配置中的filter-name
            expectedInsertions = 10000,    // 与配置保持一致
            fpp = 0.01                     // 与配置保持一致
        )
    )
    public RoleInfo getRoleInfo(Long roleId) {
        log.info("从数据库查询角色信息: {}", roleId);

        // 模拟数据库查询
        if (roleId <= 0 || roleId > 10000) {
            // 角色不存在
            return null;
        }

        return new RoleInfo(roleId, "角色" + roleId, "ROLE_" + roleId);
    }

    /**
     * 部门信息查询示例
     * <p>
     * 使用配置化预热的部门布隆过滤器防止缓存穿透
     * 对应配置文件中name为"dept"的预热配置
     */
    @TlCache(
        cacheName = "dept",
        key = "#deptId",
        l1ExpireSeconds = 900,
        l2ExpireSeconds = 7200,
        bloomFilter = @BloomFilter(
            enabled = true,
            filterName = "dept",           // 对应配置中的filter-name
            expectedInsertions = 50000,    // 与配置保持一致
            fpp = 0.01                     // 与配置保持一致
        )
    )
    public DeptInfo getDeptInfo(Long deptId) {
        log.info("从数据库查询部门信息: {}", deptId);

        // 模拟数据库查询
        if (deptId <= 0 || deptId > 50000) {
            // 部门不存在
            return null;
        }

        return new DeptInfo(deptId, "部门" + deptId, "DEPT_" + deptId);
    }

    /**
     * 用户信息实体类
     */
    public static class UserInfo {
        private Long userId;
        private String userName;
        private String email;

        public UserInfo(Long userId, String userName, String email) {
            this.userId = userId;
            this.userName = userName;
            this.email = email;
        }

        // getters and setters
        public Long getUserId() {
            return userId;
        }

        public void setUserId(Long userId) {
            this.userId = userId;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        @Override
        public String toString() {
            return "UserInfo{userId=" + userId + ", userName='" + userName + "', email='" + email + "'}";
        }
    }

    /**
     * 角色信息实体类
     */
    public static class RoleInfo {
        private Long roleId;
        private String roleName;
        private String roleKey;

        public RoleInfo(Long roleId, String roleName, String roleKey) {
            this.roleId = roleId;
            this.roleName = roleName;
            this.roleKey = roleKey;
        }

        // getters and setters
        public Long getRoleId() {
            return roleId;
        }

        public void setRoleId(Long roleId) {
            this.roleId = roleId;
        }

        public String getRoleName() {
            return roleName;
        }

        public void setRoleName(String roleName) {
            this.roleName = roleName;
        }

        public String getRoleKey() {
            return roleKey;
        }

        public void setRoleKey(String roleKey) {
            this.roleKey = roleKey;
        }

        @Override
        public String toString() {
            return "RoleInfo{roleId=" + roleId + ", roleName='" + roleName + "', roleKey='" + roleKey + "'}";
        }
    }

    /**
     * 部门信息实体类
     */
    public static class DeptInfo {
        private Long deptId;
        private String deptName;
        private String deptCode;

        public DeptInfo(Long deptId, String deptName, String deptCode) {
            this.deptId = deptId;
            this.deptName = deptName;
            this.deptCode = deptCode;
        }

        // getters and setters
        public Long getDeptId() {
            return deptId;
        }

        public void setDeptId(Long deptId) {
            this.deptId = deptId;
        }

        public String getDeptName() {
            return deptName;
        }

        public void setDeptName(String deptName) {
            this.deptName = deptName;
        }

        public String getDeptCode() {
            return deptCode;
        }

        public void setDeptCode(String deptCode) {
            this.deptCode = deptCode;
        }

        @Override
        public String toString() {
            return "DeptInfo{deptId=" + deptId + ", deptName='" + deptName + "', deptCode='" + deptCode + "'}";
        }
    }
}
