# 布隆过滤器预热配置示例
# 此配置文件展示了如何使用轻量级的布隆过滤器预热功能

tl-cache:
  # 现有的二级缓存配置保持不变
  enabled: true
  enable-l1-cache: true
  redis-key-prefix: tl-cache
  l1-max-size: 1000
  lock-strategy: STRIPED

  # 新增的布隆过滤器预热配置
  bloom-filter-warmup:
    # 启用布隆过滤器预热功能
    enabled: true

    # 预热配置列表
    configs:
      # 用户布隆过滤器预热配置
      - name: "user"
        description: "用户ID布隆过滤器预热"
        sql: "SELECT user_id FROM sys_user WHERE del_flag = '0'"
        filter-config:
          expected-insertions: 500000    # 预期50万用户
          fpp: 0.005                     # 0.5%误判率
          filter-name: "user"            # 布隆过滤器名称
          auto-init: true                # 自动初始化
          expire-seconds: -1             # 永不过期

# 使用示例：
# 在Service方法上使用@TlCache注解，配合布隆过滤器防穿透

# 示例1：用户信息缓存
# @TlCache(
#     cacheName = "user",
#     key = "#userId",
#     l1ExpireSeconds = 300,
#     l2ExpireSeconds = 1800,
#     bloomFilter = @BloomFilter(
#         enabled = true,
#         filterName = "user",           # 对应配置中的filter-name
#         expectedInsertions = 500000,   # 与配置保持一致
#         fpp = 0.005                    # 与配置保持一致
#     )
# )
# public UserInfo getUserInfo(Long userId) {
#     return userMapper.selectById(userId);
# }

# 示例2：角色信息缓存
# @TlCache(
#     cacheName = "role",
#     key = "#roleId",
#     l1ExpireSeconds = 600,
#     l2ExpireSeconds = 3600,
#     bloomFilter = @BloomFilter(
#         enabled = true,
#         filterName = "role",
#         expectedInsertions = 10000,
#         fpp = 0.01
#     )
# )
# public RoleInfo getRoleInfo(Long roleId) {
#     return roleMapper.selectById(roleId);
# }

# 配置说明：
# 1. name: 配置名称，用于标识不同的布隆过滤器，必须唯一
# 2. description: 配置描述，用于日志输出，便于调试
# 3. sql: SQL查询语句，查询结果的第一列将作为布隆过滤器的key
# 4. filter-config: 布隆过滤器配置参数
#    - expected-insertions: 预期插入的元素数量，影响布隆过滤器大小
#    - fpp: 误判率，越小占用内存越大，建议0.001-0.05之间
#    - filter-name: 布隆过滤器名称，如果为空则使用配置的name
#    - auto-init: 是否自动初始化布隆过滤器
#    - expire-seconds: 过期时间（秒），-1表示永不过期

# 注意事项：
# 1. SQL查询的第一列必须是字符串类型或可转换为字符串的类型
# 2. expected-insertions应该根据实际数据量设置，过小会影响性能，过大会浪费内存
# 3. fpp（误判率）设置过小会占用更多内存，设置过大会增加误判
# 4. 确保@BloomFilter注解中的参数与配置文件中的参数保持一致
# 5. 应用启动时会自动执行预热，如果SQL查询耗时较长，可能会影响启动时间

# 性能建议：
# 1. 对于大表查询，建议在SQL中添加适当的WHERE条件和索引
# 2. 如果数据量很大，可以考虑分批预热或异步预热
# 3. 定期监控布隆过滤器的命中率和误判率
# 4. 根据业务需求调整过期时间，避免数据过时
