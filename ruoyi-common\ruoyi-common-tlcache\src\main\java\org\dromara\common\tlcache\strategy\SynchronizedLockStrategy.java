package org.dromara.common.tlcache.strategy;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.tlcache.config.properties.TlCacheConfigProperties;
import org.dromara.common.tlcache.manager.TlCacheManager;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 同步锁策略实现
 * 使用本地synchronized锁和Redisson分布式锁
 * <p>
 * 该策略采用两级锁机制：
 * 1. 本地锁：使用synchronized关键字，确保同一JVM内的线程安全
 * 2. 分布式锁：使用Redisson分布式锁，确保多个应用实例间的数据一致性
 * <p>
 * 适用于一般并发场景，实现简单，易于理解
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Component
@RequiredArgsConstructor
@Slf4j
@ConditionalOnBean(TlCacheManager.class)
public class SynchronizedLockStrategy implements CacheLockStrategy {

    private final TlCacheManager cacheManager;
    private final RedissonClient redissonClient;
    private final TlCacheConfigProperties configProperties;

    /**
     * 用于存储每个key对应的本地锁对象
     * <p>
     * 为每个缓存键创建一个独立的锁对象，避免不相关的缓存操作互相阻塞
     * 使用ConcurrentHashMap确保线程安全地创建和获取锁对象
     */
    private final ConcurrentHashMap<String, Object> localLocks = new ConcurrentHashMap<>();

    @Override
    public Object getOrLoad(String cacheName, String key, long l1ExpireSeconds,
                            Supplier<Object> valueLoader, long l2ExpireSeconds) throws Throwable {
        // 1. 尝试从缓存获取
        Object cache = checkCache(cacheName, key, l1ExpireSeconds);
        if (cache != null) {
            return unwrapCacheValue(cache);
        }

        // 2. 缓存未命中，使用本地锁确保单个JVM内只有一个线程加载数据
        return loadWithLocalLock(cacheName, key, l1ExpireSeconds, valueLoader, l2ExpireSeconds);
    }

    @Override
    public CacheResult getOrLoadWithMetadata(String cacheName, String key, long l1ExpireSeconds,
                                             Supplier<Object> valueLoader, long l2ExpireSeconds) throws Throwable {
        // 1. 尝试从缓存获取，并记录命中层级
        CacheResult cacheResult = checkCacheWithMetadata(cacheName, key, l1ExpireSeconds);
        if (cacheResult.isCacheHit()) {
            return cacheResult;
        }

        // 2. 缓存未命中，使用本地锁确保单个JVM内只有一个线程加载数据
        Object result = loadWithLocalLock(cacheName, key, l1ExpireSeconds, valueLoader, l2ExpireSeconds);
        return CacheResult.miss(result);
    }

    /**
     * 检查L1和L2缓存
     * <p>
     * 按照L1→L2的顺序检查缓存
     * 如果L2缓存命中，会回填到L1缓存
     *
     * @param cacheName       缓存名称
     * @param key             缓存键
     * @param l1ExpireSeconds L1缓存过期时间（秒）
     * @return 缓存值，如果不存在则返回null
     */
    private Object checkCache(String cacheName, String key, long l1ExpireSeconds) {
        // 先检查L1缓存
        Object cache = cacheManager.getL1(cacheName, key, l1ExpireSeconds);
        if (cache != null) {
            log.debug("L1 缓存命中. cacheName: {}, key: {}", cacheName, key);
            return cache;
        }

        // 再检查L2缓存
        cache = cacheManager.getL2(cacheName, key);
        if (cache != null) {
            log.debug("L2 缓存命中. cacheName: {}, key: {}", cacheName, key);
            cacheManager.putL1(cacheName, key, cache, l1ExpireSeconds);
            return cache;
        }

        // 缓存未命中
        return null;
    }

    /**
     * 检查L1和L2缓存（带元数据）
     * <p>
     * 按照L1→L2的顺序检查缓存，并返回命中层级信息
     * 如果L2缓存命中，会回填到L1缓存
     *
     * @param cacheName       缓存名称
     * @param key             缓存键
     * @param l1ExpireSeconds L1缓存过期时间（秒）
     * @return 缓存结果，包含命中信息
     */
    private CacheResult checkCacheWithMetadata(String cacheName, String key, long l1ExpireSeconds) {
        // 先检查L1缓存
        Object cache = cacheManager.getL1(cacheName, key, l1ExpireSeconds);
        if (cache != null) {
            log.debug("L1 缓存命中. cacheName: {}, key: {}", cacheName, key);
            return CacheResult.hitL1(cache);
        }

        // 再检查L2缓存
        cache = cacheManager.getL2(cacheName, key);
        if (cache != null) {
            log.debug("L2 缓存命中. cacheName: {}, key: {}", cacheName, key);
            cacheManager.putL1(cacheName, key, cache, l1ExpireSeconds);
            return CacheResult.hitL2(cache);
        }

        // 缓存未命中
        return CacheResult.missWithBloomFilterCheck();
    }

    /**
     * 使用本地锁加载数据
     * <p>
     * 使用synchronized关键字确保同一JVM内只有一个线程执行加载操作
     * 采用双重检查锁定模式(Double-Checked Locking)，避免不必要的加载
     *
     * @param cacheName       缓存名称
     * @param key             缓存键
     * @param l1ExpireSeconds L1缓存过期时间（秒）
     * @param valueLoader     值加载器
     * @param l2ExpireSeconds L2缓存过期时间（秒）
     * @return 加载的数据
     * @throws Throwable 加载过程中可能抛出的异常
     */
    private Object loadWithLocalLock(String cacheName, String key, long l1ExpireSeconds,
                                     Supplier<Object> valueLoader, long l2ExpireSeconds) throws Throwable {
        String lockKey = cacheName + ":" + key;
        Object localLock = localLocks.computeIfAbsent(lockKey, k -> new Object());

        synchronized (localLock) {
            try {
                // 双重检查：进入同步块后再次检查缓存
                Object cache = checkCache(cacheName, key, l1ExpireSeconds);
                if (cache != null) {
                    log.debug("缓存命中. cacheName: {}, key: {}", cacheName, key);
                    return unwrapCacheValue(cache);
                }

                // 本地和远程缓存都未命中，执行加载
                Object result = loadDataWithDistributedLock(cacheName, key, l1ExpireSeconds, valueLoader, l2ExpireSeconds);
                return unwrapCacheValue(result);
            } finally {
                // 清理本地锁对象，防止内存泄漏
                localLocks.remove(lockKey);
            }
        }
    }

    /**
     * 使用分布式锁加载数据
     * <p>
     * 使用Redisson分布式锁确保多个应用实例间只有一个线程执行加载操作
     * 获取锁后再次检查缓存，避免重复加载
     *
     * @param cacheName       缓存名称
     * @param key             缓存键
     * @param l1ExpireSeconds L1缓存过期时间（秒）
     * @param valueLoader     值加载器
     * @param l2ExpireSeconds L2缓存过期时间（秒）
     * @return 加载的数据
     * @throws Throwable 加载过程中可能抛出的异常
     */
    private Object loadDataWithDistributedLock(String cacheName, String key, long l1ExpireSeconds,
                                               Supplier<Object> valueLoader, long l2ExpireSeconds) throws Throwable {
        TlCacheConfigProperties.LockConfig lockConfig = configProperties.getLock();

        // 检查是否启用降级机制
        if (!lockConfig.isEnableFallback()) {
            return loadWithDistributedLockOnly(cacheName, key, l1ExpireSeconds, valueLoader, l2ExpireSeconds);
        }

        // 尝试使用分布式锁，如果失败则降级到本地锁
        try {
            return loadWithDistributedLockOnly(cacheName, key, l1ExpireSeconds, valueLoader, l2ExpireSeconds);
        } catch (Exception e) {
            log.warn("分布式锁不可用，降级到本地锁模式. cacheName: {}, key: {}, 原因: {}",
                cacheName, key, e.getMessage());
            return loadWithLocalLockFallback(cacheName, key, valueLoader, l1ExpireSeconds, l2ExpireSeconds);
        }
    }

    /**
     * 仅使用分布式锁加载数据
     */
    private Object loadWithDistributedLockOnly(String cacheName, String key, long l1ExpireSeconds,
                                               Supplier<Object> valueLoader, long l2ExpireSeconds) throws Throwable {
        String lockKey = "tlcache:lock:" + cacheName + ":" + key;
        RLock lock = redissonClient.getLock(lockKey);
        boolean locked = false;

        try {
            // 使用配置的锁等待和过期时间
            TlCacheConfigProperties.LockConfig lockConfig = configProperties.getLock();
            locked = lock.tryLock(lockConfig.getWaitTimeout(), lockConfig.getLeaseTimeout(), TimeUnit.SECONDS);
            if (!locked) {
                return handleLockFailure(cacheName, key, l1ExpireSeconds);
            }

            // 获取到锁，再检查一次缓存
            Object result = cacheManager.getL2(cacheName, key);
            if (result != null) {
                log.debug("获取分布式锁后发现缓存已被其他实例更新. cacheName: {}, key: {}", cacheName, key);
                cacheManager.putL1(cacheName, key, result, l1ExpireSeconds);
                return result;
            }

            // 执行加载方法
            return loadAndCacheData(cacheName, key, valueLoader, l1ExpireSeconds, l2ExpireSeconds);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取分布式锁被中断, cacheName: {}, key: {}", cacheName, key, e);
            throw new ServiceException("获取缓存锁被中断");
        } finally {
            // 安全释放锁
            safeUnlock(lock, locked);
        }
    }

    /**
     * 降级模式：仅使用本地锁加载数据
     * <p>
     * 当分布式锁不可用时，降级为仅使用本地锁
     * 注意：此模式下可能存在多个实例同时加载相同数据的情况
     */
    private Object loadWithLocalLockFallback(String cacheName, String key, Supplier<Object> valueLoader,
                                             long l1ExpireSeconds, long l2ExpireSeconds) {
        log.info("使用本地锁降级模式加载数据. cacheName: {}, key: {}", cacheName, key);

        // 直接执行加载，不使用分布式锁
        Object dbResult = valueLoader.get();
        Object valueToCache = (dbResult == null) ? NULL_VALUE : dbResult;

        // null值缓存时间为正常时间的10%，但最少为1秒
        long l1Expire = l1ExpireSeconds;
        long l2Expire = l2ExpireSeconds;
        if (dbResult == null) {
            l1Expire = Math.max(1, (long) (l1ExpireSeconds * 0.1));
            l2Expire = Math.max(1, (long) (l2ExpireSeconds * 0.1));
        }

        // 尝试缓存到L1，L2缓存可能因为Redis不可用而失败
        try {
            cacheManager.putL1(cacheName, key, valueToCache, l1Expire);
        } catch (Exception e) {
            log.warn("降级模式下L1缓存失败: {}", e.getMessage());
        }

        try {
            cacheManager.putL2(cacheName, key, valueToCache, l2Expire);
        } catch (Exception e) {
            log.warn("降级模式下L2缓存失败，这是预期的: {}", e.getMessage());
        }

        return valueToCache;
    }

    /**
     * 加载数据并缓存
     * <p>
     * 执行值加载器获取数据，并将结果存入缓存
     * 对于null值结果，使用NULL_VALUE标记对象存储，并缩短过期时间
     *
     * @param cacheName       缓存名称
     * @param key             缓存键
     * @param valueLoader     值加载器
     * @param l1ExpireSeconds L1缓存过期时间（秒）
     * @param l2ExpireSeconds L2缓存过期时间（秒）
     * @return 加载的数据
     */
    private Object loadAndCacheData(String cacheName, String key, Supplier<Object> valueLoader,
                                    long l1ExpireSeconds, long l2ExpireSeconds) {
        log.debug("缓存未命中，从数据源加载数据: cacheName={}, key={}", cacheName, key);
        Object dbResult = valueLoader.get();
        Object valueToCache = (dbResult == null) ? NULL_VALUE : dbResult;

        // null值缓存时间为正常时间的10%，但最少为1秒
        long l1Expire = l1ExpireSeconds;
        long l2Expire = l2ExpireSeconds;
        if (dbResult == null) {
            l1Expire = Math.max(1, (long) (l1ExpireSeconds * 0.1));
            l2Expire = Math.max(1, (long) (l2ExpireSeconds * 0.1));
        }

        cacheManager.put(cacheName, key, valueToCache, l1Expire, l2Expire);
        return valueToCache;
    }

    /**
     * 处理锁获取失败情况
     * <p>
     * 当无法获取分布式锁时，使用指数退避算法进行重试
     * 这种情况通常是因为其他应用实例正在更新缓存
     *
     * @param cacheName       缓存名称
     * @param key             缓存键
     * @param l1ExpireSeconds L1缓存过期时间（秒）
     * @return 缓存值
     * @throws InterruptedException 线程被中断时抛出
     * @throws ServiceException     重试后仍无法获取缓存时抛出
     */
    private Object handleLockFailure(String cacheName, String key, long l1ExpireSeconds) throws InterruptedException {
        TlCacheConfigProperties.LockConfig lockConfig = configProperties.getLock();
        int maxRetries = lockConfig.getMaxRetries();
        long baseInterval = lockConfig.getRetryInterval();

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            // 计算指数退避延迟时间：baseInterval * 2^(attempt-1)
            long delay = baseInterval * (1L << (attempt - 1));
            // 限制最大延迟时间为5秒
            delay = Math.min(delay, 5000);

            log.warn("未能获取分布式锁，key: '{}'. 第{}次重试，等待{}ms.", key, attempt, delay);
            Thread.sleep(delay);

            // 重试获取缓存
            Object value = cacheManager.getL2(cacheName, key);
            if (value != null) {
                log.info("重试成功获取缓存, key: '{}', 重试次数: {}", key, attempt);
                cacheManager.putL1(cacheName, key, value, l1ExpireSeconds);
                return value;
            }
        }

        log.error("重试{}次后依然无法加载缓存, key: '{}'.", maxRetries, key);
        throw new ServiceException("缓存加载失败，已重试" + maxRetries + "次: " + key);
    }

    /**
     * 安全释放锁
     * <p>
     * 安全地释放Redisson分布式锁，避免异常导致锁无法释放
     * 只有当前线程持有锁时才尝试释放
     *
     * @param lock      Redisson锁对象
     * @param wasLocked 是否成功获取过锁
     */
    private void safeUnlock(RLock lock, boolean wasLocked) {
        if (wasLocked && lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
            try {
                lock.unlock();
            } catch (Exception e) {
                log.warn("释放分布式锁异常", e);
            }
        }
    }
}
