package org.dromara.common.tlcache.manager.protection;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.tlcache.config.protection.BloomFilterConfig;
import org.dromara.common.tlcache.config.protection.RandomExpireConfig;
import org.springframework.stereotype.Component;

/**
 * 缓存防护管理器
 * <p>
 * 统一管理缓存防护机制，包括：
 * - 布隆过滤器防穿透
 * - 随机过期防雪崩
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class CacheProtectionManager {

    private final BloomFilterManager bloomFilterManager;
    private final RandomExpireManager randomExpireManager;

    /**
     * 检查是否允许访问缓存
     *
     * @param cacheName         缓存名称
     * @param key               缓存key
     * @param bloomFilterConfig 布隆过滤器配置
     * @return 访问检查结果
     */
    public AccessCheckResult checkAccess(String cacheName, String key, BloomFilterConfig bloomFilterConfig) {
        try {
            // 布隆过滤器检查（防穿透）
            if (bloomFilterConfig != null && bloomFilterConfig.isEnabled()) {
                if (!bloomFilterManager.mightContain(cacheName, key, bloomFilterConfig)) {
                    log.debug("布隆过滤器拦截: cacheName={}, key={}", cacheName, key);
                    return AccessCheckResult.blocked("布隆过滤器拦截，数据不存在");
                }
            }

            return AccessCheckResult.allowed();
        } catch (Exception e) {
            log.error("访问检查失败: cacheName={}, key={}", cacheName, key, e);
            return AccessCheckResult.error("访问检查失败: " + e.getMessage());
        }
    }


    /**
     * 计算缓存过期时间（包含随机过期）
     *
     * @param baseL1ExpireSeconds L1基础过期时间
     * @param baseL2ExpireSeconds L2基础过期时间
     * @param randomExpireConfig  随机过期配置
     * @return 过期时间结果
     */
    public ExpireTimeResult calculateExpireTime(long baseL1ExpireSeconds, long baseL2ExpireSeconds,
                                                RandomExpireConfig randomExpireConfig) {
        try {
            long l1ExpireSeconds = baseL1ExpireSeconds;
            long l2ExpireSeconds = baseL2ExpireSeconds;

            // 应用随机过期时间
            if (randomExpireConfig != null && randomExpireConfig.isEnabled()) {
                l1ExpireSeconds = randomExpireManager.calculateL1RandomExpire(l1ExpireSeconds, randomExpireConfig);
                l2ExpireSeconds = randomExpireManager.calculateL2RandomExpire(l2ExpireSeconds, randomExpireConfig);
            }

            return ExpireTimeResult.success(l1ExpireSeconds, l2ExpireSeconds);
        } catch (Exception e) {
            log.error("计算过期时间失败: baseL1={}, baseL2={}", baseL1ExpireSeconds, baseL2ExpireSeconds, e);
            return ExpireTimeResult.error("计算过期时间失败: " + e.getMessage());
        }
    }

    /**
     * 添加key到布隆过滤器
     *
     * @param cacheName 缓存名称
     * @param key       缓存key
     * @param config    布隆过滤器配置
     * @return 是否添加成功
     */
    public boolean addToBloomFilter(String cacheName, String key, BloomFilterConfig config) {
        if (config != null && config.isEnabled()) {
            return bloomFilterManager.add(cacheName, key, config);
        }
        return true;
    }

    /**
     * 访问检查结果
     */
    @Getter
    public static class AccessCheckResult {
        private final boolean allowed;
        private final String reason;
        private final String error;

        private AccessCheckResult(boolean allowed, String reason, String error) {
            this.allowed = allowed;
            this.reason = reason;
            this.error = error;
        }

        public static AccessCheckResult allowed() {
            return new AccessCheckResult(true, null, null);
        }

        public static AccessCheckResult blocked(String reason) {
            return new AccessCheckResult(false, reason, null);
        }

        public static AccessCheckResult error(String error) {
            return new AccessCheckResult(false, null, error);
        }

        public boolean hasError() {
            return error != null;
        }
    }


    /**
     * 过期时间计算结果
     */
    @Getter
    public static class ExpireTimeResult {
        private final long l1ExpireSeconds;
        private final long l2ExpireSeconds;
        private final String error;

        private ExpireTimeResult(long l1ExpireSeconds, long l2ExpireSeconds, String error) {
            this.l1ExpireSeconds = l1ExpireSeconds;
            this.l2ExpireSeconds = l2ExpireSeconds;
            this.error = error;
        }

        public static ExpireTimeResult success(long l1ExpireSeconds, long l2ExpireSeconds) {
            return new ExpireTimeResult(l1ExpireSeconds, l2ExpireSeconds, null);
        }

        public static ExpireTimeResult error(String error) {
            return new ExpireTimeResult(0, 0, error);
        }

        public boolean hasError() {
            return error != null;
        }
    }
}
