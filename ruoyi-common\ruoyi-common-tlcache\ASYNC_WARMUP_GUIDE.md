# 🚀 布隆过滤器异步预热功能使用指南

## 📋 概述

布隆过滤器异步预热功能是对原有预热功能的重要升级，通过异步并行的方式进行预热，显著提升了应用启动性能。

## 🎯 功能特点

- **异步执行**：预热过程不阻塞应用启动，提升启动速度
- **并行预热**：多个布隆过滤器可以并行预热，提升预热效率
- **完整日志**：保持详细的预热日志，便于监控和调试
- **错误隔离**：单个预热失败不影响其他预热任务
- **无缝升级**：配置方式保持不变，现有配置无需修改

## 🔄 升级说明

### 主要变化

1. **BloomFilterWarmupRunner**：
   - 保持 `ApplicationRunner` 接口，在应用启动时触发
   - 预热逻辑改为异步执行，不阻塞启动流程
   - 简化代码结构，主要负责启动异步任务

2. **AsyncBloomFilterService**：
   - 新增 `asyncWarmupAllBloomFilters()` 方法，支持并行预热
   - 新增 `asyncWarmupSingleBloomFilter()` 方法，处理单个预热任务
   - 利用 `tlCacheExecutor` 线程池进行异步执行

### 性能提升

- **启动时间**：应用启动不再等待预热完成，启动时间显著缩短
- **预热效率**：多个布隆过滤器并行预热，总预热时间减少
- **资源利用**：充分利用多核CPU资源，提升预热速度

## 📝 配置示例

配置方式保持不变，现有配置无需修改：

```yaml
tl-cache:
  bloom-filter-warmup:
    enabled: true
    configs:
      # 用户布隆过滤器
      - name: "user"
        description: "用户ID布隆过滤器预热"
        sql: "SELECT user_id FROM sys_user WHERE del_flag = '0'"
        filter-config:
          expected-insertions: 500000
          fpp: 0.005
          filter-name: "user"
          auto-init: true
          expire-seconds: -1
          
      # 角色布隆过滤器
      - name: "role"
        description: "角色ID布隆过滤器预热"
        sql: "SELECT role_id FROM sys_role WHERE del_flag = '0'"
        filter-config:
          expected-insertions: 10000
          fpp: 0.01
          filter-name: "role"
```

## 📊 日志输出

### 启动日志
```
2025-07-23 10:00:00.123 INFO  --- 启动布隆过滤器异步预热，共2个配置
2025-07-23 10:00:00.125 INFO  --- 布隆过滤器异步预热任务已启动，预热将在后台进行
```

### 预热过程日志
```
2025-07-23 10:00:00.200 INFO  --- 开始异步预热布隆过滤器，共2个配置
2025-07-23 10:00:00.201 INFO  --- 开始异步预热布隆过滤器: user - 用户ID布隆过滤器预热
2025-07-23 10:00:00.202 INFO  --- 开始异步预热布隆过滤器: role - 角色ID布隆过滤器预热
2025-07-23 10:00:01.456 INFO  --- 布隆过滤器 role 异步预热完成，查询数据: 50, 成功添加: 50
2025-07-23 10:00:02.789 INFO  --- 布隆过滤器 user 异步预热完成，查询数据: 12345, 成功添加: 12345
2025-07-23 10:00:02.790 INFO  --- 布隆过滤器异步预热完成，成功: 2, 失败: 0
```

## 🔧 技术实现

### 异步执行架构

```
ApplicationRunner (启动时)
    ↓
AsyncBloomFilterService.asyncWarmupAllBloomFilters() [@Async("tlCacheExecutor")]
    ↓
并行执行多个 CompletableFuture.runAsync(warmupSingleBloomFilter, tlCacheExecutor)
    ↓
每个任务在线程池中独立执行：SQL查询 → 批量添加 → 统计信息
```

### 线程池使用说明

- **主预热任务**：使用 `@Async("tlCacheExecutor")` 在线程池中异步执行
- **并行子任务**：使用 `CompletableFuture.runAsync(task, tlCacheExecutor)` 确保在同一线程池中并行执行
- **避免内部调用问题**：不依赖Spring AOP的内部方法调用，直接使用线程池执行器

### 线程池配置

使用专用的 `tlCacheExecutor` 线程池：
- 核心线程数：CPU核心数
- 最大线程数：CPU核心数
- 队列容量：200
- 拒绝策略：CallerRunsPolicy

### 错误处理

- 单个预热失败不影响其他任务
- 详细的错误日志记录
- 最终统计成功和失败的数量

## 🚀 使用建议

1. **监控日志**：关注预热完成的日志，确保预热成功
2. **配置优化**：根据实际数据量调整 `expected-insertions` 参数
3. **SQL优化**：确保预热SQL查询性能良好，避免影响预热速度
4. **分批预热**：对于大量数据，可以考虑分批配置多个预热任务

## 🔍 故障排查

### 常见问题

1. **预热任务未启动**
   - 检查 `tl-cache.bloom-filter-warmup.enabled` 是否为 `true`
   - 检查配置是否正确

2. **预热失败**
   - 查看错误日志，通常是SQL查询问题
   - 检查数据库连接和权限

3. **预热速度慢**
   - 优化SQL查询性能
   - 检查数据库负载
   - 考虑调整线程池配置

### 日志级别

- `INFO`：预热进度和结果
- `DEBUG`：详细的执行过程
- `ERROR`：错误信息和异常堆栈

## 📈 性能对比

| 指标 | 同步预热 | 异步预热 | 提升 |
|------|----------|----------|------|
| 应用启动时间 | 等待预热完成 | 立即启动 | 显著提升 |
| 预热总时间 | 串行执行 | 并行执行 | 50%+ |
| 资源利用率 | 单线程 | 多线程 | 多倍提升 |
| 用户体验 | 启动阻塞 | 无感知 | 极大改善 |

异步预热功能在保持原有功能完整性的基础上，显著提升了系统性能和用户体验。
