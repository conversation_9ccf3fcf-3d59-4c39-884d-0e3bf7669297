package org.dromara.common.tlcache.manager.protection;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.tlcache.config.protection.BloomFilterConfig;
import org.redisson.api.RBloomFilter;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 布隆过滤器管理器
 * <p>
 * 负责管理Redis布隆过滤器的创建、初始化和操作
 * 用于防止缓存穿透攻击
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class BloomFilterManager {

    private final RedissonClient redissonClient;

    /**
     * 布隆过滤器缓存，避免重复创建
     */
    private final ConcurrentHashMap<String, RBloomFilter<String>> bloomFilterCache = new ConcurrentHashMap<>();

    /**
     * 获取或创建布隆过滤器
     *
     * @param cacheName 缓存名称
     * @param config    布隆过滤器配置
     * @return 布隆过滤器实例
     */
    public RBloomFilter<String> getOrCreateBloomFilter(String cacheName, BloomFilterConfig config) {
        if (!config.isEnabled()) {
            return null;
        }

        String filterKey = config.getFilterKey(cacheName);

        return bloomFilterCache.computeIfAbsent(filterKey, key -> {
            try {
                RBloomFilter<String> bloomFilter = redissonClient.getBloomFilter(key);

                // 检查布隆过滤器是否已经初始化
                if (!bloomFilter.isExists() && config.isAutoInit()) {
                    log.info("初始化布隆过滤器: {}, 预期插入数量: {}, 误判率: {}",
                        key, config.getExpectedInsertions(), config.getFpp());

                    // 初始化布隆过滤器
                    bloomFilter.tryInit(config.getExpectedInsertions(), config.getFpp());

                    // 设置过期时间
                    if (config.getExpireSeconds() > 0) {
                        bloomFilter.expire(java.time.Duration.ofSeconds(config.getExpireSeconds()));
                    }
                }

                return bloomFilter;
            } catch (Exception e) {
                log.error("创建布隆过滤器失败: {}", key, e);
                return null;
            }
        });
    }

    /**
     * 检查key是否可能存在
     *
     * @param cacheName 缓存名称
     * @param key       要检查的key
     * @param config    布隆过滤器配置
     * @return true表示可能存在，false表示一定不存在
     */
    public boolean mightContain(String cacheName, String key, BloomFilterConfig config) {
        if (!config.isEnabled()) {
            // 如果未启用布隆过滤器，默认放行
            return true;
        }

        try {
            RBloomFilter<String> bloomFilter = getOrCreateBloomFilter(cacheName, config);
            if (bloomFilter == null) {
                log.warn("布隆过滤器不可用，默认允许访问: cacheName={}, key={}", cacheName, key);
                return true;
            }

            boolean result = bloomFilter.contains(key);
            log.debug("布隆过滤器检查结果: cacheName={}, key={}, result={}", cacheName, key, result);
            return result;
        } catch (Exception e) {
            log.error("布隆过滤器检查失败: cacheName={}, key={}", cacheName, key, e);
            // 出错时默认允许访问
            return true;
        }
    }

    /**
     * 添加key到布隆过滤器
     *
     * @param cacheName 缓存名称
     * @param key       要添加的key
     * @param config    布隆过滤器配置
     * @return 是否添加成功
     */
    public boolean add(String cacheName, String key, BloomFilterConfig config) {
        if (!config.isEnabled()) {
            return true;
        }

        try {
            RBloomFilter<String> bloomFilter = getOrCreateBloomFilter(cacheName, config);
            if (bloomFilter == null) {
                log.warn("布隆过滤器不可用，无法添加key: cacheName={}, key={}", cacheName, key);
                return false;
            }

            boolean result = bloomFilter.add(key);
            log.debug("添加key到布隆过滤器: cacheName={}, key={}, result={}", cacheName, key, result);
            return result;
        } catch (Exception e) {
            log.error("添加key到布隆过滤器失败: cacheName={}, key={}", cacheName, key, e);
            return false;
        }
    }

    /**
     * 批量添加key到布隆过滤器
     *
     * @param cacheName 缓存名称
     * @param keys      要添加的key列表
     * @param config    布隆过滤器配置
     * @return 成功添加的数量
     */
    public long addAll(String cacheName, Collection<String> keys, BloomFilterConfig config) {
        if (!config.isEnabled()) {
            return 0;
        }

        try {
            RBloomFilter<String> bloomFilter = getOrCreateBloomFilter(cacheName, config);
            if (bloomFilter == null) {
                log.warn("布隆过滤器不可用，无法批量添加key: cacheName={}", cacheName);
                return 0;
            }
            long count = bloomFilter.add(keys);

            log.debug("批量添加key到布隆过滤器: cacheName={}, 成功添加数量={}", cacheName, count);
            return count;
        } catch (Exception e) {
            log.error("批量添加key到布隆过滤器失败: cacheName={}", cacheName, e);
            return 0;
        }
    }

    /**
     * 清空布隆过滤器
     *
     * @param cacheName 缓存名称
     * @param config    布隆过滤器配置
     * @return 是否清空成功
     */
    public boolean clear(String cacheName, BloomFilterConfig config) {
        if (!config.isEnabled()) {
            return true;
        }

        try {
            String filterKey = config.getFilterKey(cacheName);
            RBloomFilter<String> bloomFilter = bloomFilterCache.get(filterKey);

            if (bloomFilter != null) {
                bloomFilter.delete();
                bloomFilterCache.remove(filterKey);
                log.info("清空布隆过滤器: {}", filterKey);
                return true;
            }

            return true;
        } catch (Exception e) {
            log.error("清空布隆过滤器失败: cacheName={}", cacheName, e);
            return false;
        }
    }

    /**
     * 重建布隆过滤器
     *
     * @param cacheName 缓存名称
     * @param config    布隆过滤器配置
     * @return 是否重建成功
     */
    public boolean rebuild(String cacheName, BloomFilterConfig config) {
        if (!config.isEnabled()) {
            return true;
        }

        try {
            // 先清空现有的布隆过滤器
            clear(cacheName, config);

            // 重新创建布隆过滤器
            RBloomFilter<String> bloomFilter = getOrCreateBloomFilter(cacheName, config);

            log.info("重建布隆过滤器: cacheName={}", cacheName);
            return bloomFilter != null;
        } catch (Exception e) {
            log.error("重建布隆过滤器失败: cacheName={}", cacheName, e);
            return false;
        }
    }

    /**
     * 获取布隆过滤器统计信息
     *
     * @param cacheName 缓存名称
     * @param config    布隆过滤器配置
     * @return 统计信息
     */
    public BloomFilterStats getStats(String cacheName, BloomFilterConfig config) {
        if (!config.isEnabled()) {
            return BloomFilterStats.disabled();
        }

        try {
            RBloomFilter<String> bloomFilter = getOrCreateBloomFilter(cacheName, config);
            if (bloomFilter == null) {
                return BloomFilterStats.unavailable();
            }

            return BloomFilterStats.builder()
                .enabled(true)
                .available(true)
                .filterKey(config.getFilterKey(cacheName))
                .expectedInsertions(config.getExpectedInsertions())
                .fpp(config.getFpp())
                .count(bloomFilter.count())
                .build();
        } catch (Exception e) {
            log.error("获取布隆过滤器统计信息失败: cacheName={}", cacheName, e);
            return BloomFilterStats.error(e.getMessage());
        }
    }

    /**
     * 布隆过滤器统计信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BloomFilterStats {
        private boolean enabled;
        private boolean available;
        private String filterKey;
        private long expectedInsertions;
        private double fpp;
        private long count;
        private String error;

        public static BloomFilterStats disabled() {
            BloomFilterStats stats = new BloomFilterStats();
            stats.enabled = false;
            return stats;
        }

        public static BloomFilterStats unavailable() {
            BloomFilterStats stats = new BloomFilterStats();
            stats.enabled = true;
            stats.available = false;
            return stats;
        }

        public static BloomFilterStats error(String error) {
            BloomFilterStats stats = unavailable();
            stats.error = error;
            return stats;
        }
    }
}
