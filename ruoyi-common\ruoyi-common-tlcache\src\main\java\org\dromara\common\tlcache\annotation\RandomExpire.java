package org.dromara.common.tlcache.annotation;

import org.dromara.common.tlcache.enums.RandomStrategy;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 随机过期配置注解
 * <p>
 * 用于在@TlCache注解中配置随机过期防雪崩机制
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RandomExpire {

    /**
     * 是否启用随机过期，默认false
     */
    boolean enabled() default false;

    /**
     * 随机时间的最小值（秒），默认0
     */
    long minSeconds() default 0;

    /**
     * 随机时间的最大值（秒），默认300
     */
    long maxSeconds() default 300;

    /**
     * 随机策略，默认UNIFORM（均匀分布）
     */
    RandomStrategy strategy() default RandomStrategy.UNIFORM;

    /**
     * 是否对L1缓存也应用随机过期，默认false
     */
    boolean applyToL1() default false;

    /**
     * 随机种子，默认-1（使用系统时间）
     */
    long randomSeed() default -1;
}
