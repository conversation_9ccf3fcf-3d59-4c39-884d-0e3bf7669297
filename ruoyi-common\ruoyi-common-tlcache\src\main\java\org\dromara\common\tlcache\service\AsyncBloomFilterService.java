package org.dromara.common.tlcache.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.tlcache.config.properties.BloomFilterWarmupProperties;
import org.dromara.common.tlcache.config.protection.BloomFilterConfig;
import org.dromara.common.tlcache.manager.protection.BloomFilterManager;
import org.dromara.common.tlcache.manager.protection.CacheProtectionManager;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 异步布隆过滤器服务
 * <p>
 * 提供异步的布隆过滤器操作，避免阻塞主流程
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AsyncBloomFilterService {

    private final CacheProtectionManager cacheProtectionManager;
    private final BloomFilterManager bloomFilterManager;

    @Qualifier("tlCacheExecutor")
    private final Executor tlCacheExecutor;

    /**
     * 异步添加key到布隆过滤器
     * <p>
     * 此方法异步执行，不会阻塞主流程，提升响应性能
     *
     * @param cacheName 缓存名称
     * @param key       缓存key
     * @param config    布隆过滤器配置
     */
    @Async("tlCacheExecutor")
    public void asyncAddToBloomFilter(String cacheName, String key, BloomFilterConfig config) {
        try {
            if (config != null && config.isEnabled()) {
                boolean success = cacheProtectionManager.addToBloomFilter(cacheName, key, config);
                if (success) {
                    log.debug("异步添加到布隆过滤器成功: cacheName={}, key={}", cacheName, key);
                } else {
                    log.warn("异步添加到布隆过滤器失败: cacheName={}, key={}", cacheName, key);
                }
            }
        } catch (Exception e) {
            log.error("异步添加到布隆过滤器异常: cacheName={}, key={}", cacheName, key, e);
        }
    }

    /**
     * 同步添加key到布隆过滤器（兜底方法）
     * <p>
     * 当异步执行器不可用时的兜底方法
     *
     * @param cacheName 缓存名称
     * @param key       缓存key
     * @param config    布隆过滤器配置
     */
    public void syncAddToBloomFilter(String cacheName, String key, BloomFilterConfig config) {
        try {
            if (config != null && config.isEnabled()) {
                cacheProtectionManager.addToBloomFilter(cacheName, key, config);
                log.debug("同步添加到布隆过滤器: cacheName={}, key={}", cacheName, key);
            }
        } catch (Exception e) {
            log.error("同步添加到布隆过滤器失败: cacheName={}, key={}", cacheName, key, e);
        }
    }

    /**
     * 异步预热所有布隆过滤器
     * <p>
     * 并行预热多个布隆过滤器，提升预热效率
     *
     * @param configs      预热配置列表
     * @param jdbcTemplate JDBC模板
     */
    @Async("tlCacheExecutor")
    public void asyncWarmupAllBloomFilters(List<BloomFilterWarmupProperties.WarmupConfig> configs,
                                           JdbcTemplate jdbcTemplate) {
        log.info("开始异步预热布隆过滤器，共{}个配置", configs.size());
        for (BloomFilterWarmupProperties.WarmupConfig config : configs) {
            CompletableFuture.runAsync(() ->
                warmupSingleBloomFilter(config, jdbcTemplate), tlCacheExecutor);
        }
    }


    /**
     * 预热单个布隆过滤器的具体实现
     * <p>
     * 执行单个布隆过滤器的完整预热流程
     *
     * @param config       预热配置
     * @param jdbcTemplate JDBC模板
     */
    private void warmupSingleBloomFilter(BloomFilterWarmupProperties.WarmupConfig config,
                                         JdbcTemplate jdbcTemplate) {
        try {
            log.info("开始异步预热布隆过滤器: {} - {}", config.getName(), config.getDescription());

            // 创建布隆过滤器配置
            BloomFilterConfig bloomConfig = createBloomFilterConfig(config);

            // 执行SQL查询获取数据
            List<String> keys = executeQuery(config, jdbcTemplate);

            if (CollUtil.isEmpty(keys)) {
                log.warn("布隆过滤器 {} 没有查询到数据，SQL: {}", config.getName(), config.getSql());
                return;
            }

            // 批量添加到布隆过滤器
            long addedCount = bloomFilterManager.addAll(config.getName(), keys, bloomConfig);

            log.info("布隆过滤器 {} 异步预热完成，查询数据: {}, 成功添加: {}",
                config.getName(), keys.size(), addedCount);

            // 获取布隆过滤器统计信息
            try {
                BloomFilterManager.BloomFilterStats stats = bloomFilterManager.getStats(config.getName(), bloomConfig);
                log.info("布隆过滤器 {} 统计信息: {}", config.getName(), stats);
            } catch (Exception e) {
                log.debug("获取布隆过滤器统计信息失败: {}", config.getName(), e);
            }

        } catch (Exception e) {
            log.error("异步预热布隆过滤器失败: {} - {}", config.getName(), config.getDescription(), e);
            throw new RuntimeException("异步预热布隆过滤器失败: " + config.getName(), e);
        }
    }

    /**
     * 执行SQL查询获取数据
     *
     * @param config       预热配置
     * @param jdbcTemplate JDBC模板
     * @return 查询结果列表
     */
    private List<String> executeQuery(BloomFilterWarmupProperties.WarmupConfig config, JdbcTemplate jdbcTemplate) {
        try {
            log.debug("执行SQL查询: {}", config.getSql());
            return jdbcTemplate.queryForList(config.getSql(), String.class);
        } catch (Exception e) {
            log.error("执行SQL查询失败: {} - SQL: {}", config.getName(), config.getSql(), e);
            throw new RuntimeException("执行SQL查询失败: " + config.getName(), e);
        }
    }

    /**
     * 创建布隆过滤器配置
     *
     * @param config 预热配置
     * @return 布隆过滤器配置
     */
    private BloomFilterConfig createBloomFilterConfig(BloomFilterWarmupProperties.WarmupConfig config) {
        BloomFilterWarmupProperties.FilterConfig filterConfig = config.getFilterConfig();

        BloomFilterConfig bloomConfig = new BloomFilterConfig();
        bloomConfig.setEnabled(true);
        bloomConfig.setExpectedInsertions(filterConfig.getExpectedInsertions());
        bloomConfig.setFpp(filterConfig.getFpp());

        // 如果没有指定filterName，使用配置的name
        String filterName = filterConfig.getFilterName();
        if (StrUtil.isBlank(filterName)) {
            filterName = config.getName();
        }
        bloomConfig.setFilterName(filterName);

        bloomConfig.setAutoInit(filterConfig.isAutoInit());
        bloomConfig.setExpireSeconds(filterConfig.getExpireSeconds());

        // 验证配置
        bloomConfig.validate();

        return bloomConfig;
    }
}
