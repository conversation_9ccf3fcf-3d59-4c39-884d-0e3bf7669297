package org.dromara.common.tlcache.annotation;

import org.dromara.common.tlcache.enums.LockStrategyType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 二级缓存注解
 * 用于标记需要使用二级缓存的方法。
 * 二级缓存系统包含：
 * 1. L1缓存：本地内存缓存(Caffeine)，访问速度快，但数据仅在当前JVM实例有效
 * 2. L2缓存：分布式缓存(Redis)，可在多个应用实例间共享数据
 * <p>
 * 使用示例：
 * <pre>
 * {@code
 * @TlCache(cacheName = "userCache", key = "#userId", l1ExpireSeconds = 60, l2ExpireSeconds = 300)
 * public UserDTO getUserById(Long userId) {
 *     // 方法实现
 * }
 * }
 * </pre>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface TlCache {

    /**
     * 缓存名称，用于隔离不同业务的缓存。
     * 必填。
     *
     * @return 缓存名称
     */
    String cacheName();

    /**
     * SpEL表达式，用于从方法参数中动态生成缓存的key。
     * 例如：'#id' 或 '#user.id'。
     * 必填。
     *
     * @return 用于生成缓存键的SpEL表达式
     */
    String key();

    /**
     * L1 Caffeine缓存的过期时间，单位为秒。
     * 默认60秒。
     *
     * @return L1缓存过期时间（秒）
     */
    long l1ExpireSeconds() default 60;

    /**
     * L2 Redis缓存的过期时间，单位为秒。
     * 默认300秒。
     *
     * @return L2缓存过期时间（秒）
     */
    long l2ExpireSeconds() default 300;

    /**
     * 锁策略类型，用于指定该方法使用的锁策略。
     * 默认为AUTO，表示使用全局配置的锁策略。
     * <p>
     * 可选值：
     * - AUTO: 使用全局配置的锁策略
     * - SYNCHRONIZED: 使用同步锁策略，适用于一般并发场景
     * - COMPLETABLE_FUTURE: 使用CompletableFuture策略，适用于高并发场景
     * - STRIPED: 使用分段锁策略，适用于高并发热点数据
     *
     * @return 锁策略类型
     */
    LockStrategyType lockStrategy() default LockStrategyType.AUTO;

    /**
     * 布隆过滤器配置，用于防止缓存穿透
     * <p>
     * 布隆过滤器可以高效地判断一个元素是否可能存在于集合中，
     * 用于防止缓存穿透攻击
     *
     * @return 布隆过滤器配置
     */
    BloomFilter bloomFilter() default @BloomFilter;

    /**
     * 随机过期配置，用于防止缓存雪崩
     * <p>
     * 通过在基础过期时间上增加随机时间，避免大量缓存同时过期
     *
     * @return 随机过期配置
     */
    RandomExpire randomExpire() default @RandomExpire;
}
