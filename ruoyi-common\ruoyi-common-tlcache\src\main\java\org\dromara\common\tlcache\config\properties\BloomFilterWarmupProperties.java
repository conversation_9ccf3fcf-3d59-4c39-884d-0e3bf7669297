package org.dromara.common.tlcache.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.List;

/**
 * 布隆过滤器预热配置属性
 * <p>
 * 用于配置布隆过滤器的预热参数，支持通过YAML配置文件
 * 定义多个布隆过滤器的初始化策略
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
@ConfigurationProperties(prefix = "tl-cache.bloom-filter-warmup")
public class BloomFilterWarmupProperties {

    /**
     * 是否启用布隆过滤器预热，默认false
     */
    private boolean enabled = false;

    /**
     * 布隆过滤器预热配置列表
     */
    private List<WarmupConfig> configs = new ArrayList<>();

    /**
     * 验证所有配置的有效性
     */
    public void validate() {
        if (!enabled) {
            return;
        }

        if (configs.isEmpty()) {
            throw new IllegalArgumentException("启用布隆过滤器预热时，configs不能为空");
        }

        // 检查配置名称是否重复
        List<String> names = configs.stream()
            .map(WarmupConfig::getName)
            .toList();

        long distinctCount = names.stream().distinct().count();
        if (distinctCount != names.size()) {
            throw new IllegalArgumentException("布隆过滤器预热配置中存在重复的name");
        }

        // 验证每个配置
        for (WarmupConfig config : configs) {
            config.validate();
        }
    }

    /**
     * 预热配置
     */
    @Data
    public static class WarmupConfig {
        /**
         * 配置名称，用于标识不同的布隆过滤器
         */
        private String name;

        /**
         * 配置描述，用于日志输出
         */
        private String description;

        /**
         * SQL查询语句，用于获取需要预热的数据
         * 查询结果的第一列将作为布隆过滤器的key
         */
        private String sql;

        /**
         * 布隆过滤器配置参数
         */
        private FilterConfig filterConfig;

        /**
         * 验证配置的有效性
         */
        public void validate() {
            if (name == null || name.trim().isEmpty()) {
                throw new IllegalArgumentException("布隆过滤器预热配置的name不能为空");
            }
            if (sql == null || sql.trim().isEmpty()) {
                throw new IllegalArgumentException("布隆过滤器预热配置的sql不能为空: " + name);
            }
            if (filterConfig == null) {
                throw new IllegalArgumentException("布隆过滤器预热配置的filterConfig不能为空: " + name);
            }
            filterConfig.validate();
        }
    }

    /**
     * 布隆过滤器配置参数
     */
    @Data
    public static class FilterConfig {
        /**
         * 预期插入的元素数量，默认100万
         */
        private long expectedInsertions = 1000000L;

        /**
         * 误判率，默认1%
         */
        private double fpp = 0.01;

        /**
         * 布隆过滤器名称，如果为空则使用配置的name
         */
        private String filterName;

        /**
         * 是否自动初始化，默认true
         */
        private boolean autoInit = true;

        /**
         * 过期时间（秒），默认-1（永不过期）
         */
        private long expireSeconds = -1;

        /**
         * 验证配置的有效性
         */
        public void validate() {
            if (expectedInsertions <= 0) {
                throw new IllegalArgumentException("expectedInsertions must be positive");
            }
            if (fpp <= 0 || fpp >= 1) {
                throw new IllegalArgumentException("fpp must be between 0 and 1");
            }
        }
    }
}
