package org.dromara.common.tlcache.config.protection;

import lombok.Data;
import org.dromara.common.tlcache.enums.RandomStrategy;

/**
 * 随机过期配置
 * <p>
 * 用于配置随机过期时间机制，防止缓存雪崩
 * 通过在基础过期时间上增加随机时间，避免大量缓存同时过期
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class RandomExpireConfig {

    /**
     * 是否启用随机过期，默认false
     */
    private boolean enabled = false;

    /**
     * 随机时间的最小值（秒），默认0
     * <p>
     * 实际过期时间 = 基础过期时间 + random(minSeconds, maxSeconds)
     */
    private long minSeconds = 0;

    /**
     * 随机时间的最大值（秒），默认300（5分钟）
     * <p>
     * 建议设置为基础过期时间的10-20%
     */
    private long maxSeconds = 300;

    /**
     * 随机策略类型，默认UNIFORM（均匀分布）
     */
    private RandomStrategy strategy = RandomStrategy.UNIFORM;

    /**
     * 是否对L1和L2缓存都应用随机过期，默认false（仅L2）
     * <p>
     * 通常只对L2缓存应用随机过期，因为L1缓存过期时间较短
     */
    private boolean applyToL1 = false;

    /**
     * 随机种子，默认使用系统时间
     * <p>
     * 可以设置固定种子以便测试时获得可预测的结果
     */
    private Long randomSeed;

    /**
     * 创建默认配置
     *
     * @return 默认的随机过期配置
     */
    public static RandomExpireConfig defaultConfig() {
        RandomExpireConfig config = new RandomExpireConfig();
        config.setEnabled(true);
        config.setMinSeconds(0);
        config.setMaxSeconds(300); // 5分钟随机范围
        return config;
    }

    /**
     * 创建短期随机配置
     *
     * @return 短期的随机过期配置
     */
    public static RandomExpireConfig shortTermConfig() {
        RandomExpireConfig config = new RandomExpireConfig();
        config.setEnabled(true);
        config.setMinSeconds(0);
        config.setMaxSeconds(60); // 1分钟随机范围
        return config;
    }

    /**
     * 创建长期随机配置
     *
     * @return 长期的随机过期配置
     */
    public static RandomExpireConfig longTermConfig() {
        RandomExpireConfig config = new RandomExpireConfig();
        config.setEnabled(true);
        config.setMinSeconds(0);
        config.setMaxSeconds(1800); // 30分钟随机范围
        return config;
    }

    /**
     * 计算随机过期时间
     *
     * @param baseExpireSeconds 基础过期时间
     * @return 加上随机时间后的过期时间
     */
    public long calculateRandomExpire(long baseExpireSeconds) {
        if (!enabled || minSeconds == maxSeconds) {
            return baseExpireSeconds;
        }

        long randomSeconds = generateRandomSeconds();
        return baseExpireSeconds + randomSeconds;
    }

    /**
     * 生成随机秒数
     *
     * @return 随机秒数
     */
    private long generateRandomSeconds() {
        if (minSeconds >= maxSeconds) {
            return minSeconds;
        }

        java.util.Random random = randomSeed != null
            ? new java.util.Random(randomSeed)
            : new java.util.Random();

        return switch (strategy) {
            case UNIFORM -> minSeconds + (long) (random.nextDouble() * (maxSeconds - minSeconds));
            case NORMAL -> generateNormalRandom(random);
            case EXPONENTIAL -> generateExponentialRandom(random);
        };
    }

    /**
     * 生成正态分布随机数
     */
    private long generateNormalRandom(java.util.Random random) {
        double mean = (minSeconds + maxSeconds) / 2.0;
        double stdDev = (maxSeconds - minSeconds) / 6.0; // 99.7%的值在3σ范围内

        double value = random.nextGaussian() * stdDev + mean;

        // 确保值在有效范围内
        value = Math.max(minSeconds, Math.min(maxSeconds, value));
        return (long) value;
    }

    /**
     * 生成指数分布随机数
     */
    private long generateExponentialRandom(java.util.Random random) {
        double lambda = 2.0 / (maxSeconds - minSeconds); // 调整参数使大部分值在范围内
        double value = -Math.log(1 - random.nextDouble()) / lambda + minSeconds;

        // 确保值在有效范围内
        value = Math.min(maxSeconds, value);
        return (long) value;
    }

    /**
     * 验证配置参数的有效性
     *
     * @throws IllegalArgumentException 如果配置参数无效
     */
    public void validate() {
        if (minSeconds < 0) {
            throw new IllegalArgumentException("minSeconds must be non-negative");
        }
        if (maxSeconds < minSeconds) {
            throw new IllegalArgumentException("maxSeconds must be greater than or equal to minSeconds");
        }
    }
}
