package org.dromara.common.tlcache.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 布隆过滤器配置注解
 * <p>
 * 用于在@TlCache注解中配置布隆过滤器防穿透机制
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface BloomFilter {

    /**
     * 是否启用布隆过滤器，默认false
     * 开启布隆过滤器，需要进行数据预热,将数据预热到布隆过滤器中,否则会直接返回null
     */
    boolean enabled() default false;

    /**
     * 预期插入的元素数量，默认100万
     */
    long expectedInsertions() default 1000000L;

    /**
     * 误判率，默认1%
     */
    double fpp() default 0.01;

    /**
     * 布隆过滤器名称，默认为空（使用cacheName）
     * 全名 "bf:" + filterName.trim();
     */
    String filterName() default "";

    /**
     * 是否自动初始化，默认true
     */
    boolean autoInit() default true;

    /**
     * 过期时间（秒），默认-1（永不过期）
     */
    long expireSeconds() default -1;
}
