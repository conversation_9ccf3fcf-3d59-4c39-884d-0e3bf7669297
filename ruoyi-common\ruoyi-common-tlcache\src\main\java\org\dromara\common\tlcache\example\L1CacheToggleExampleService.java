package org.dromara.common.tlcache.example;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.tlcache.annotation.TlCache;
import org.springframework.stereotype.Service;

/**
 * L1缓存开关使用示例
 * <p>
 * 展示如何通过配置控制L1缓存的启用和禁用
 * 以及不同配置下的缓存行为差异
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Slf4j
public class L1CacheToggleExampleService {

    /**
     * 普通缓存方法示例
     * <p>
     * 根据 enable-l1-cache 配置的不同，此方法的缓存行为会有所不同：
     * - enable-l1-cache: true  → L1(Caffeine) + L2(Redis)
     * - enable-l1-cache: false → 仅 L2(Redis)
     */
    @TlCache(
        cacheName = "userData",
        key = "#userId",
        l1ExpireSeconds = 300,  // 5分钟本地缓存
        l2ExpireSeconds = 1800  // 30分钟分布式缓存
    )
    public UserData getUserData(Long userId) {
        log.info("从数据库加载用户数据: {}", userId);
        // 模拟数据库查询耗时
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        return new UserData(userId, "用户" + userId, "user" + userId + "@example.com");
    }

    /**
     * 高频访问的配置数据
     * <p>
     * 配置数据通常变化较少，适合缓存
     * 当禁用L1缓存时，每次访问都需要通过网络访问Redis
     */
    @TlCache(
        cacheName = "appConfig",
        key = "#configKey",
        l1ExpireSeconds = 3600, // 1小时本地缓存
        l2ExpireSeconds = 86400 // 24小时分布式缓存
    )
    public AppConfig getAppConfig(String configKey) {
        log.info("从数据库加载应用配置: {}", configKey);
        // 模拟配置查询
        return new AppConfig(configKey, "配置值-" + configKey, System.currentTimeMillis());
    }

    /**
     * 统计数据示例
     * <p>
     * 统计数据计算复杂，缓存价值高
     * L1缓存的存在可以显著减少计算开销
     */
    @TlCache(
        cacheName = "userStats",
        key = "#userId + ':' + #period",
        l1ExpireSeconds = 600,  // 10分钟本地缓存
        l2ExpireSeconds = 3600  // 1小时分布式缓存
    )
    public UserStats calculateUserStats(Long userId, String period) {
        log.info("计算用户统计数据: userId={}, period={}", userId, period);
        // 模拟复杂的统计计算
        try {
            Thread.sleep(200);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 模拟统计结果
        int baseValue = userId.intValue() * 10;
        return new UserStats(
            userId,
            period,
            baseValue + 100,      // 访问次数
            baseValue + 50,       // 订单数量
            baseValue * 2 + 1000  // 积分
        );
    }

    /**
     * 商品信息示例
     * <p>
     * 商品信息是典型的热点数据
     * L1缓存可以极大提升访问性能
     */
    @TlCache(
        cacheName = "productInfo",
        key = "#productId",
        l1ExpireSeconds = 180,  // 3分钟本地缓存
        l2ExpireSeconds = 900   // 15分钟分布式缓存
    )
    public ProductInfo getProductInfo(Long productId) {
        log.info("从数据库加载商品信息: {}", productId);
        // 模拟数据库查询
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        return new ProductInfo(
            productId,
            "商品-" + productId,
            "这是商品" + productId + "的详细描述",
            99.99 + productId
        );
    }

    // 示例数据类
    public record UserData(Long userId, String userName, String email) {
    }

    public record AppConfig(String key, String value, Long updateTime) {
    }

    public record UserStats(Long userId, String period, Integer visitCount, Integer orderCount, Integer points) {
    }

    public record ProductInfo(Long productId, String productName, String description, Double price) {
    }
}

/*
配置示例和效果说明：

1. 启用L1缓存（默认配置）：
```yaml
tl-cache:
  enable-l1-cache: true
```
效果：
- 首次访问：数据源 → L2缓存 → L1缓存 → 返回结果
- 后续访问：L1缓存直接返回（极快）
- L1过期后：L2缓存 → L1缓存 → 返回结果
- L2也过期：数据源 → L2缓存 → L1缓存 → 返回结果

2. 禁用L1缓存：
```yaml
tl-cache:
  enable-l1-cache: false
```
效果：
- 首次访问：数据源 → L2缓存 → 返回结果
- 后续访问：L2缓存 → 返回结果（需要网络访问）
- L2过期后：数据源 → L2缓存 → 返回结果

性能对比：
- 启用L1缓存：本地访问，微秒级响应
- 禁用L1缓存：网络访问，毫秒级响应

内存使用：
- 启用L1缓存：占用JVM堆内存
- 禁用L1缓存：不占用JVM堆内存

适用场景：
- 内存充足 + 性能优先 → 启用L1缓存
- 内存紧张 + 一致性优先 → 禁用L1缓存
*/
