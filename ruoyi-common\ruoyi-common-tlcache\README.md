# 二级缓存模块

## 1. 简介

二级缓存模块提供了基于Caffeine(L1)和Redis(L2)的两级缓存实现，支持通过注解方式使用缓存，并提供分布式环境下的缓存一致性保证。

## 2. 特性

- 两级缓存：本地缓存(Caffeine) + 分布式缓存(Redis)
- 注解驱动：通过@TlCache注解轻松使用缓存
- 分布式一致性：多节点环境下保持缓存一致性
- 多种锁策略：支持不同并发场景下的锁策略
- 空值缓存：防止缓存穿透
- 自动配置：开箱即用，配置简单

## 3. 配置说明

```yaml
# 二级缓存配置
tl-cache:
  # 是否启用二级缓存，默认为true
  enabled: true
  # Redis Key的全局前缀，用于防止多应用间缓存键冲突
  redis-key-prefix: tl-cache
  # 是否启用分布式缓存一致性，默认为true
  distributed-consistency: true
  # L1缓存最大条目数，默认10000
  l1-max-size: 10000
  # 锁策略类型：SYNCHRONIZED(默认)或COMPLETABLE_FUTURE
  lock-strategy: SYNCHRONIZED
```

## 4. 使用方式

### 4.1 通过注解使用

```java
@Service
public class UserService {
    
    @TlCache(cacheName = "users", key = "#userId", l1ExpireSeconds = 60, l2ExpireSeconds = 300)
    public UserDTO getUserById(Long userId) {
        // 方法体...
        // 结果会被自动缓存
    }
}
```

### 4.2 直接使用TlCacheManager

```java
@Service
@RequiredArgsConstructor
public class ProductService {
    private final TlCacheManager cacheManager;
    
    public void updateProduct(ProductDTO product) {
        // 更新数据库
        updateProductInDb(product);
        
        // 更新缓存
        String key = product.getId().toString();
        cacheManager.put("products", key, product, 60, 300);
    }
    
    public void deleteProduct(Long productId) {
        // 删除数据库记录
        deleteProductFromDb(productId);
        
        // 移除缓存
        cacheManager.evict("products", productId.toString());
    }
}
```

## 5. @TlCache注解参数

| 参数名          | 类型   | 必填 | 默认值 | 说明                                          |
| --------------- | ------ | ---- | ------ | --------------------------------------------- |
| cacheName       | String | 是   | -      | 缓存名称，用于隔离不同业务的缓存              |
| key             | String | 是   | -      | SpEL表达式，用于从方法参数中动态生成缓存的key |
| l1ExpireSeconds | long   | 否   | 60     | L1 Caffeine缓存的过期时间，单位为秒           |
| l2ExpireSeconds | long   | 否   | 300    | L2 Redis缓存的过期时间，单位为秒              |

## 6. 最佳实践

1. 为不同业务设置不同的cacheName
2. 合理设置缓存过期时间，避免数据过期不一致
3. 对于写操作，记得主动更新或清除缓存
4. 在高并发场景下，建议使用COMPLETABLE_FUTURE锁策略
5. 避免缓存大对象，以减少内存占用和网络传输开销

## 7. 分布式一致性

本模块通过Redisson的RTopic实现了分布式环境下的缓存一致性：

1. 当一个节点更新或删除缓存时，会发布一条消息通知其他节点
2. 其他节点收到消息后，会使对应的本地缓存失效
3. 这确保了在分布式环境下，所有节点的缓存数据保持一致

默认情况下，分布式一致性功能是启用的。如果您的应用是单节点部署，可以通过配置`distributed-consistency: false`来禁用此功能，以减少不必要的消息发布。

## 8. 性能测试

- 测试环境：4核心8线程  32g内存  Windows环境  redis版本7.4.2
- 测试场景: 10000次请求,20个线程并发访问一个方法，方法内部调用了数据库查询
- 参数设置: l1ExpireSeconds = 6, l2ExpireSeconds = 10
- COMPLETABLE_FUTURE 策略  用时34秒
- SYNCHRONIZED 策略  用时46秒
- 无缓存             用时52秒  cpu占用90%+
