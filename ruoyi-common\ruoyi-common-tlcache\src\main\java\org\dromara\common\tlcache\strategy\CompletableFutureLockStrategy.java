package org.dromara.common.tlcache.strategy;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.tlcache.config.properties.TlCacheConfigProperties;
import org.dromara.common.tlcache.manager.TlCacheManager;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Supplier;

/**
 * CompletableFuture锁策略实现
 * 使用CompletableFuture和Redisson分布式锁
 * 适用于高并发场景，减少分布式锁竞争
 * <p>
 * 该策略采用"领导者-跟随者"模式：
 * 1. 同一JVM内，第一个请求相同缓存键的线程成为"领导者"，负责实际加载数据
 * 2. 后续请求相同缓存键的线程成为"跟随者"，等待领导者完成加载
 * 3. 使用CompletableFuture实现非阻塞等待
 * 4. 只有领导者线程会获取分布式锁，减少锁竞争
 * <p>
 * 相比SynchronizedLockStrategy，该策略在高并发场景下性能更好
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Component
@Slf4j
@ConditionalOnBean(TlCacheManager.class)
public class CompletableFutureLockStrategy implements CacheLockStrategy {

    private final TlCacheManager cacheManager;
    private final RedissonClient redissonClient;
    private final TlCacheConfigProperties configProperties;

    /**
     * JVM本地加载缓存，防止同一JVM内多线程重复加载
     * 键为缓存名称:键，值为正在加载的CompletableFuture
     * <p>
     * 用于实现"领导者-跟随者"模式：
     * - 第一个请求某个键的线程会创建Future并成为领导者
     * - 后续请求相同键的线程会等待已存在的Future完成
     * - Future完成后会从映射表中移除，避免内存泄漏
     */
    private final ConcurrentHashMap<String, FutureWrapper> loadingFutures = new ConcurrentHashMap<>();

    /**
     * 定时清理任务，用于清理超时的Future
     */
    private final ScheduledExecutorService cleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r, "TlCache-Future-Cleanup");
        t.setDaemon(true);
        return t;
    });

    /**
     * 构造函数，启动定时清理任务
     */
    public CompletableFutureLockStrategy(TlCacheManager cacheManager,
                                         RedissonClient redissonClient,
                                         TlCacheConfigProperties configProperties) {
        this.cacheManager = cacheManager;
        this.redissonClient = redissonClient;
        this.configProperties = configProperties;

        // 每5分钟清理一次超时的Future
        cleanupExecutor.scheduleWithFixedDelay(this::cleanupTimeoutFutures, 5, 5, TimeUnit.MINUTES);
    }

    /**
     * 清理超时的Future，防止内存泄漏
     */
    private void cleanupTimeoutFutures() {
        try {
            long currentTime = System.currentTimeMillis();
            int futureTimeout = configProperties.getLock().getFutureTimeout();
            long timeoutMillis = futureTimeout * 1000L * 2; // 超时时间的2倍作为清理阈值

            int cleanedCount = 0;
            for (var entry : loadingFutures.entrySet()) {
                FutureWrapper wrapper = entry.getValue();
                if (currentTime - wrapper.createTime > timeoutMillis) {
                    // Future已经超时很久，可能是异常情况，强制清理
                    if (wrapper.future.isDone() || wrapper.future.isCancelled()) {
                        loadingFutures.remove(entry.getKey());
                        cleanedCount++;
                    } else {
                        // 取消未完成的Future
                        wrapper.future.cancel(true);
                        loadingFutures.remove(entry.getKey());
                        cleanedCount++;
                        log.warn("强制清理超时Future: {}", entry.getKey());
                    }
                }
            }

            if (cleanedCount > 0) {
                log.info("清理了{}个超时的Future，当前活跃Future数量: {}", cleanedCount, loadingFutures.size());
            }
        } catch (Exception e) {
            log.error("清理超时Future时发生异常", e);
        }
    }

    /**
     * 获取缓存值，如果不存在则加载
     *
     * @param cacheName       缓存名称
     * @param key             缓存键
     * @param l1ExpireSeconds L1缓存过期时间（秒）
     * @param valueLoader     值加载器，用于在缓存未命中时加载数据
     * @param l2ExpireSeconds L2缓存过期时间（秒）
     * @return 缓存值或加载的值
     * @throws Throwable 加载过程中可能抛出的异常
     */
    @Override
    public Object getOrLoad(String cacheName, String key, long l1ExpireSeconds,
                            Supplier<Object> valueLoader, long l2ExpireSeconds) throws Throwable {
        // 1. 快速路径：尝试从缓存获取数据
        Object cachedValue = checkCache(cacheName, key, l1ExpireSeconds);
        if (cachedValue != null) {
            log.debug("缓存命中. cacheName: {}, key: {}", cacheName, key);
            return unwrapCacheValue(cachedValue);
        }

        // 2. 慢路径：缓存未命中，使用CompletableFuture处理并发加载
        // 构建唯一的future键，用于标识正在加载的缓存项
        final String futureKey = cacheName + ":" + key;

        try {
            // 使用CompletableFuture确保相同的key只被一个线程加载
            return getWithFuture(futureKey, () -> {
                try {
                    // 在分布式锁保护下加载数据
                    return loadDataWithDistributedLock(cacheName, key, l1ExpireSeconds, valueLoader, l2ExpireSeconds);
                } catch (Throwable e) {
                    log.error("加载缓存数据异常. cacheName: {}, key: {}", cacheName, key, e);
                    if (e instanceof RuntimeException) {
                        throw (RuntimeException) e;
                    }
                    throw new RuntimeException("加载缓存数据失败: " + e.getMessage(), e);
                }
            }, key);
        } catch (Throwable e) {
            // 确保异常信息包含足够的上下文
            log.error("缓存加载过程异常. cacheName: {}, key: {}", cacheName, key, e);
            throw e;
        }
    }

    /**
     * 检查L1和L2缓存
     * 先检查本地缓存，再检查分布式缓存
     *
     * @param cacheName       缓存名称
     * @param key             缓存键
     * @param l1ExpireSeconds L1缓存过期时间（秒）
     * @return 缓存值，如果不存在则返回null
     */
    private Object checkCache(String cacheName, String key, long l1ExpireSeconds) {
        // 1. 先检查L1缓存（本地内存缓存，访问速度最快）
        Object cachedValue = cacheManager.getL1(cacheName, key, l1ExpireSeconds);
        if (cachedValue != null) {
            log.debug("L1缓存命中. cacheName: {}, key: {}", cacheName, key);
            return cachedValue;
        }

        // 2. 再检查L2缓存（Redis分布式缓存）
        cachedValue = cacheManager.getL2(cacheName, key);
        if (cachedValue != null) {
            // L2缓存命中，回填到L1缓存
            log.debug("L2缓存命中，回填到L1. cacheName: {}, key: {}", cacheName, key);

            // 异步回填L1缓存，避免阻塞当前线程
            Object finalCachedValue = cachedValue;
            CompletableFuture.runAsync(() -> {
                try {
                    cacheManager.putL1(cacheName, key, finalCachedValue, l1ExpireSeconds);
                } catch (Exception e) {
                    log.warn("回填L1缓存异常. cacheName: {}, key: {}", cacheName, key, e);
                }
            });

            return cachedValue;
        }

        // 缓存未命中
        log.debug("缓存未命中. cacheName: {}, key: {}", cacheName, key);
        return null;
    }

    /**
     * 使用CompletableFuture获取或加载数据
     * 该方法确保相同的key在同一时间只有一个线程执行实际的加载操作
     * <p>
     * 实现"领导者-跟随者"模式的核心方法：
     * 1. 检查是否已有线程正在加载相同的键
     * 2. 如果有，成为跟随者并等待结果
     * 3. 如果没有，尝试成为领导者并负责加载数据
     * 4. 使用ConcurrentHashMap的原子操作确保线程安全
     *
     * @param futureKey  用于标识加载任务的键
     * @param dataLoader 数据加载器
     * @param logKey     用于日志记录的键
     * @return 加载的数据
     * @throws Throwable 加载过程中可能抛出的异常
     */
    private Object getWithFuture(String futureKey, Supplier<Object> dataLoader, String logKey) throws Throwable {
        // 1. 尝试获取已存在的Future，避免重复加载
        FutureWrapper existingWrapper = loadingFutures.get(futureKey);
        if (existingWrapper != null) {
            // 已有其他线程在加载，当前线程等待结果
            log.debug("发现进行中的加载任务，等待结果. key: '{}'", logKey);
            return handleExistingFuture(existingWrapper.future, logKey);
        }

        // 2. 创建新的Future并尝试成为领导者线程
        CompletableFuture<Object> newFuture = new CompletableFuture<>();
        FutureWrapper newWrapper = new FutureWrapper(newFuture);

        // 使用原子操作尝试放入新的Future
        FutureWrapper previousWrapper = loadingFutures.putIfAbsent(futureKey, newWrapper);

        if (previousWrapper == null) {
            // 成功成为领导者线程，负责加载数据
            log.debug("成为领导者线程，开始加载数据. key: '{}'", logKey);
            return handleLeaderProcess(newFuture, dataLoader, futureKey, logKey);
        } else {
            // 在尝试成为领导者的过程中，另一个线程抢先一步
            log.debug("竞争失败，成为跟随者线程. key: '{}'", logKey);
            return handleExistingFuture(previousWrapper.future, logKey);
        }
    }

    /**
     * 处理领导者线程的加载过程
     * 负责实际执行数据加载并通知所有等待的线程
     * <p>
     * 领导者线程的职责：
     * 1. 执行实际的数据加载操作
     * 2. 完成Future，通知所有等待的跟随者线程
     * 3. 清理Future映射，避免内存泄漏
     * 4. 记录性能指标（加载耗时）
     *
     * @param loadingFuture 用于通知其他线程的Future
     * @param dataLoader    数据加载器
     * @param futureKey     用于清理Future映射的键
     * @param logKey        用于日志记录的键
     * @return 加载的数据
     */
    private Object handleLeaderProcess(CompletableFuture<Object> loadingFuture,
                                       Supplier<Object> dataLoader,
                                       String futureKey,
                                       String logKey) {
        long startTime = System.currentTimeMillis();
        try {
            // 领导者线程负责加载数据
            Object result = dataLoader.get();

            // 记录加载耗时
            long elapsed = System.currentTimeMillis() - startTime;
            log.debug("数据加载完成. key: '{}', 耗时: {}ms", logKey, elapsed);

            // 通知所有等待的线程
            loadingFuture.complete(result);
            return unwrapCacheValue(result);
        } catch (Throwable e) {
            // 加载失败，通知所有等待的线程
            log.error("数据加载失败. key: '{}', 原因: {}", logKey, e.getMessage());
            loadingFuture.completeExceptionally(e);
            throw e;
        } finally {
            // 清理，避免内存泄漏
            loadingFutures.remove(futureKey);
        }
    }

    /**
     * 处理跟随者线程的等待过程
     * 等待领导者线程完成数据加载
     * <p>
     * 跟随者线程的职责：
     * 1. 等待领导者线程完成数据加载
     * 2. 处理等待超时情况
     * 3. 处理领导者线程可能抛出的异常
     * 4. 记录性能指标（等待耗时）
     * <p>
     * 设置了超时机制，避免因领导者线程异常导致跟随者线程无限等待
     *
     * @param future 等待的Future
     * @param logKey 用于日志记录的键
     * @return 加载的数据
     * @throws Throwable 等待过程中可能抛出的异常
     */
    private Object handleExistingFuture(CompletableFuture<Object> future, String logKey) throws Throwable {
        long startTime = System.currentTimeMillis();
        try {
            // 跟随者线程在此等待，使用配置的超时时间
            int futureTimeout = configProperties.getLock().getFutureTimeout();
            Object result = future.get(futureTimeout, TimeUnit.SECONDS);

            // 记录等待耗时
            long elapsed = System.currentTimeMillis() - startTime;
            log.debug("等待数据加载完成. key: '{}', 等待耗时: {}ms", logKey, elapsed);

            return unwrapCacheValue(result);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("等待缓存加载被中断. key: '{}', 等待耗时: {}ms", logKey, System.currentTimeMillis() - startTime);
            throw new ServiceException("等待缓存加载被中断: " + logKey);
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            log.warn("缓存加载执行出错. key: '{}', 错误: {}, 等待耗时: {}ms",
                logKey, cause.getMessage(), System.currentTimeMillis() - startTime);
            throw cause;
        } catch (TimeoutException e) {
            int futureTimeout = configProperties.getLock().getFutureTimeout();
            log.warn("等待缓存加载超时. key: '{}', 等待超过: {}秒", logKey, futureTimeout);
            throw new ServiceException("等待缓存加载超时: " + logKey);
        }
    }

    /**
     * 使用分布式锁加载数据
     * <p>
     * 在领导者线程中使用Redisson分布式锁确保多个应用实例间的数据一致性
     * 获取锁后再次检查缓存，避免重复加载
     *
     * @param cacheName       缓存名称
     * @param key             缓存键
     * @param l1ExpireSeconds L1缓存过期时间（秒）
     * @param valueLoader     值加载器
     * @param l2ExpireSeconds L2缓存过期时间（秒）
     * @return 加载的数据
     * @throws Throwable 加载过程中可能抛出的异常
     */
    private Object loadDataWithDistributedLock(String cacheName, String key, long l1ExpireSeconds,
                                               Supplier<Object> valueLoader, long l2ExpireSeconds) throws Throwable {
        TlCacheConfigProperties.LockConfig lockConfig = configProperties.getLock();

        // 检查是否启用降级机制
        if (!lockConfig.isEnableFallback()) {
            return loadWithDistributedLockOnly(cacheName, key, l1ExpireSeconds, valueLoader, l2ExpireSeconds);
        }

        // 尝试使用分布式锁，如果失败则降级到本地锁
        try {
            return loadWithDistributedLockOnly(cacheName, key, l1ExpireSeconds, valueLoader, l2ExpireSeconds);
        } catch (Exception e) {
            log.warn("分布式锁不可用，降级到本地锁模式. cacheName: {}, key: {}, 原因: {}",
                cacheName, key, e.getMessage());
            return loadWithLocalLockFallback(cacheName, key, valueLoader, l1ExpireSeconds, l2ExpireSeconds);
        }
    }

    /**
     * 仅使用分布式锁加载数据
     */
    private Object loadWithDistributedLockOnly(String cacheName, String key, long l1ExpireSeconds,
                                               Supplier<Object> valueLoader, long l2ExpireSeconds) throws Throwable {
        String lockKey = "tlcache:lock:" + cacheName + ":" + key;
        RLock lock = redissonClient.getLock(lockKey);
        boolean locked = false;

        try {
            // 使用配置的锁等待和过期时间
            TlCacheConfigProperties.LockConfig lockConfig = configProperties.getLock();
            locked = lock.tryLock(lockConfig.getWaitTimeout(), lockConfig.getLeaseTimeout(), TimeUnit.SECONDS);
            if (!locked) {
                return handleLockFailure(cacheName, key, l1ExpireSeconds);
            }

            // 获取到锁，再检查一次缓存
            Object result = cacheManager.getL2(cacheName, key);
            if (result != null) {
                log.debug("获取分布式锁后发现缓存已被其他实例更新. cacheName: {}, key: {}", cacheName, key);
                cacheManager.putL1(cacheName, key, result, l1ExpireSeconds);
                return result;
            }

            // 执行加载方法
            return loadAndCacheData(cacheName, key, valueLoader, l1ExpireSeconds, l2ExpireSeconds);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取分布式锁被中断, cacheName: {}, key: {}", cacheName, key, e);
            throw new ServiceException("获取缓存锁被中断");
        } finally {
            // 安全释放锁
            safeUnlock(lock, locked);
        }
    }

    /**
     * 降级模式：仅使用本地锁加载数据
     * <p>
     * 当分布式锁不可用时，降级为仅使用本地锁
     * 注意：此模式下可能存在多个实例同时加载相同数据的情况
     */
    private Object loadWithLocalLockFallback(String cacheName, String key, Supplier<Object> valueLoader,
                                             long l1ExpireSeconds, long l2ExpireSeconds) {
        log.info("使用本地锁降级模式加载数据. cacheName: {}, key: {}", cacheName, key);

        // 直接执行加载，不使用分布式锁
        Object dbResult = valueLoader.get();
        Object valueToCache = (dbResult == null) ? NULL_VALUE : dbResult;

        // null值缓存时间为正常时间的10%，但最少为1秒
        long l1Expire = l1ExpireSeconds;
        long l2Expire = l2ExpireSeconds;
        if (dbResult == null) {
            l1Expire = Math.max(1, (long) (l1ExpireSeconds * 0.1));
            l2Expire = Math.max(1, (long) (l2ExpireSeconds * 0.1));
        }

        // 尝试缓存到L1，L2缓存可能因为Redis不可用而失败
        try {
            cacheManager.putL1(cacheName, key, valueToCache, l1Expire);
        } catch (Exception e) {
            log.warn("降级模式下L1缓存失败: {}", e.getMessage());
        }

        try {
            cacheManager.putL2(cacheName, key, valueToCache, l2Expire);
        } catch (Exception e) {
            log.warn("降级模式下L2缓存失败，这是预期的: {}", e.getMessage());
        }

        return valueToCache;
    }

    /**
     * 加载数据并缓存
     * <p>
     * 执行值加载器获取数据，并将结果存入缓存
     * 对于null值结果，使用NULL_VALUE标记对象存储，并缩短过期时间
     *
     * @param cacheName       缓存名称
     * @param key             缓存键
     * @param valueLoader     值加载器
     * @param l1ExpireSeconds L1缓存过期时间（秒）
     * @param l2ExpireSeconds L2缓存过期时间（秒）
     * @return 加载的数据
     */
    private Object loadAndCacheData(String cacheName, String key, Supplier<Object> valueLoader,
                                    long l1ExpireSeconds, long l2ExpireSeconds) {
        log.debug("缓存未命中，从数据源加载数据: cacheName={}, key={}", cacheName, key);
        Object dbResult = valueLoader.get();
        Object valueToCache = (dbResult == null) ? NULL_VALUE : dbResult;

        // null值缓存时间为正常时间的10%，但最少为1秒
        long l1Expire = l1ExpireSeconds;
        long l2Expire = l2ExpireSeconds;
        if (dbResult == null) {
            l1Expire = Math.max(1, (long) (l1ExpireSeconds * 0.1));
            l2Expire = Math.max(1, (long) (l2ExpireSeconds * 0.1));
        }

        cacheManager.put(cacheName, key, valueToCache, l1Expire, l2Expire);
        return valueToCache;
    }

    /**
     * 处理锁获取失败情况
     * <p>
     * 当无法获取分布式锁时，使用指数退避算法进行重试
     * 这种情况通常是因为其他应用实例正在更新缓存
     *
     * @param cacheName       缓存名称
     * @param key             缓存键
     * @param l1ExpireSeconds L1缓存过期时间（秒）
     * @return 缓存值
     * @throws InterruptedException 线程被中断时抛出
     * @throws ServiceException     重试后仍无法获取缓存时抛出
     */
    private Object handleLockFailure(String cacheName, String key, long l1ExpireSeconds) throws InterruptedException {
        TlCacheConfigProperties.LockConfig lockConfig = configProperties.getLock();
        int maxRetries = lockConfig.getMaxRetries();
        long baseInterval = lockConfig.getRetryInterval();

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            // 计算指数退避延迟时间：baseInterval * 2^(attempt-1)
            long delay = baseInterval * (1L << (attempt - 1));
            // 限制最大延迟时间为5秒
            delay = Math.min(delay, 5000);

            log.warn("未能获取分布式锁，key: '{}'. 第{}次重试，等待{}ms.", key, attempt, delay);
            Thread.sleep(delay);

            // 重试获取缓存
            Object value = cacheManager.getL2(cacheName, key);
            if (value != null) {
                log.info("重试成功获取缓存, key: '{}', 重试次数: {}", key, attempt);
                cacheManager.putL1(cacheName, key, value, l1ExpireSeconds);
                return value;
            }
        }

        log.error("重试{}次后依然无法加载缓存, key: '{}'.", maxRetries, key);
        throw new ServiceException("缓存加载失败，已重试" + maxRetries + "次: " + key);
    }

    /**
     * 安全释放锁
     * <p>
     * 安全地释放Redisson分布式锁，避免异常导致锁无法释放
     * 只有当前线程持有锁时才尝试释放
     *
     * @param lock      Redisson锁对象
     * @param wasLocked 是否成功获取过锁
     */
    private void safeUnlock(RLock lock, boolean wasLocked) {
        if (wasLocked && lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
            try {
                lock.unlock();
            } catch (Exception e) {
                log.warn("释放分布式锁异常", e);
            }
        }
    }

    /**
     * Future包装器，包含Future和创建时间，用于超时清理
     */
    private static class FutureWrapper {
        final CompletableFuture<Object> future;
        final long createTime;

        FutureWrapper(CompletableFuture<Object> future) {
            this.future = future;
            this.createTime = System.currentTimeMillis();
        }
    }
}
