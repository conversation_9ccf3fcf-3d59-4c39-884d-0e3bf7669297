package org.dromara.common.tlcache.config.protection;

import lombok.Data;

/**
 * 布隆过滤器配置
 * <p>
 * 用于配置布隆过滤器防穿透机制的相关参数
 * 布隆过滤器可以高效地判断一个元素是否可能存在于集合中，
 * 用于防止缓存穿透攻击
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class BloomFilterConfig {

    /**
     * 是否启用布隆过滤器，默认false
     */
    private boolean enabled = false;

    /**
     * 预期插入的元素数量，默认100万
     * <p>
     * 这个值影响布隆过滤器的大小和性能
     * 应该根据实际业务数据量进行设置
     */
    private long expectedInsertions = 1000000L;

    /**
     * 误判率（False Positive Probability），默认1%
     * <p>
     * 误判率越低，布隆过滤器占用的内存越大
     * 建议设置在0.01-0.05之间
     */
    private double fpp = 0.01;

    /**
     * 布隆过滤器名称，默认为空（使用cacheName）
     * <p>
     * 如果不设置，将使用 "bf:{cacheName}" 作为Redis中的key
     * 可以设置自定义名称以便多个缓存共享同一个布隆过滤器
     */
    private String filterName;

    /**
     * 是否自动初始化布隆过滤器，默认true
     * <p>
     * 如果设置为true，系统会在首次使用时自动创建布隆过滤器
     * 如果设置为false，需要手动初始化布隆过滤器
     */
    private boolean autoInit = true;

    /**
     * 布隆过滤器的过期时间（秒），默认-1（永不过期）
     * <p>
     * 设置过期时间可以定期重建布隆过滤器，避免数据过时
     * 建议根据业务数据的更新频率设置合适的过期时间
     */
    private long expireSeconds = -1;

    /**
     * 是否启用预热模式，默认false
     * <p>
     * 启用预热模式时，系统会在应用启动时将现有数据加载到布隆过滤器中
     * 适用于数据量不大且相对稳定的场景
     */
    private boolean enableWarmup = false;

    /**
     * 预热数据的批量大小，默认1000
     * <p>
     * 预热时每次批量处理的数据量
     * 避免一次性加载过多数据导致内存压力
     */
    private int warmupBatchSize = 1000;

    /**
     * 创建默认配置
     *
     * @return 默认的布隆过滤器配置
     */
    public static BloomFilterConfig defaultConfig() {
        BloomFilterConfig config = new BloomFilterConfig();
        config.setEnabled(true);
        config.setExpectedInsertions(100000L);
        config.setFpp(0.01);
        return config;
    }

    /**
     * 创建高性能配置（较高误判率，较小内存占用）
     *
     * @return 高性能的布隆过滤器配置
     */
    public static BloomFilterConfig highPerformanceConfig() {
        BloomFilterConfig config = new BloomFilterConfig();
        config.setEnabled(true);
        config.setExpectedInsertions(1000000L);
        config.setFpp(0.05);  // 5%误判率
        return config;
    }

    /**
     * 创建高精度配置（较低误判率，较大内存占用）
     *
     * @return 高精度的布隆过滤器配置
     */
    public static BloomFilterConfig highAccuracyConfig() {
        BloomFilterConfig config = new BloomFilterConfig();
        config.setEnabled(true);
        config.setExpectedInsertions(500000L);
        config.setFpp(0.001);  // 0.1%误判率
        return config;
    }

    /**
     * 获取布隆过滤器在Redis中的完整key名称
     *
     * @param cacheName 缓存名称
     * @return 布隆过滤器的Redis key
     */
    public String getFilterKey(String cacheName) {
        if (filterName != null && !filterName.trim().isEmpty()) {
            return "bf:" + filterName.trim();
        }
        return "bf:" + cacheName;
    }

    /**
     * 验证配置参数的有效性
     *
     * @throws IllegalArgumentException 如果配置参数无效
     */
    public void validate() {
        if (expectedInsertions <= 0) {
            throw new IllegalArgumentException("expectedInsertions must be positive");
        }
        if (fpp <= 0 || fpp >= 1) {
            throw new IllegalArgumentException("fpp must be between 0 and 1");
        }
        if (warmupBatchSize <= 0) {
            throw new IllegalArgumentException("warmupBatchSize must be positive");
        }
    }
}
