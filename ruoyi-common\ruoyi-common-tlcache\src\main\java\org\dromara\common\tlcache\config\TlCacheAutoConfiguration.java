package org.dromara.common.tlcache.config;

import lombok.RequiredArgsConstructor;
import org.dromara.common.tlcache.aspect.TlCacheAspect;
import org.dromara.common.tlcache.config.properties.BloomFilterWarmupProperties;
import org.dromara.common.tlcache.config.properties.TlCacheConfigProperties;
import org.dromara.common.tlcache.manager.TlCacheManager;
import org.dromara.common.tlcache.manager.protection.BloomFilterManager;
import org.dromara.common.tlcache.manager.protection.CacheProtectionManager;
import org.dromara.common.tlcache.runner.BloomFilterWarmupRunner;
import org.dromara.common.tlcache.service.AsyncBloomFilterService;
import org.dromara.common.tlcache.strategy.CompletableFutureLockStrategy;
import org.dromara.common.tlcache.strategy.LockStrategyFactory;
import org.dromara.common.tlcache.strategy.StripedLockStrategy;
import org.dromara.common.tlcache.strategy.SynchronizedLockStrategy;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * 二级缓存自动配置
 * <p>
 * 负责自动配置二级缓存系统的各个组件：
 * - 缓存管理器
 * - 锁策略工厂
 * - 缓存切面
 * <p>
 * 该配置类只有在tl-cache.enabled=true（默认为true）时才会生效
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties({TlCacheConfigProperties.class, BloomFilterWarmupProperties.class})
@ConditionalOnProperty(prefix = "tl-cache", name = "enabled", havingValue = "true", matchIfMissing = true)
public class TlCacheAutoConfiguration {
    private static final Logger log = LoggerFactory.getLogger(TlCacheAutoConfiguration.class);

    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnBean(TlCacheConfigProperties.class)
    public TlCacheManager tlCacheManager(RedissonClient redissonClient,
                                         TlCacheConfigProperties tlCacheConfigProperties) {
        log.info("初始化二级缓存管理器");
        return new TlCacheManager(redissonClient, tlCacheConfigProperties);
    }

    /**
     * 注册锁策略工厂
     * <p>
     * 创建并配置锁策略工厂，用于根据配置选择适当的锁策略实现
     *
     * @param synchronizedLockStrategy      同步锁策略实现
     * @param completableFutureLockStrategy CompletableFuture锁策略实现
     * @param stripedLockStrategy           分段锁策略实现
     * @param tlCacheConfigProperties       缓存配置属性
     * @return 锁策略工厂实例
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnBean({SynchronizedLockStrategy.class, CompletableFutureLockStrategy.class, StripedLockStrategy.class})
    public LockStrategyFactory lockStrategyFactory(SynchronizedLockStrategy synchronizedLockStrategy,
                                                   CompletableFutureLockStrategy completableFutureLockStrategy,
                                                   StripedLockStrategy stripedLockStrategy,
                                                   TlCacheConfigProperties tlCacheConfigProperties) {
        log.info("初始化锁策略工厂");
        return new LockStrategyFactory(synchronizedLockStrategy, completableFutureLockStrategy, stripedLockStrategy, tlCacheConfigProperties);
    }

    /**
     * 注册二级缓存切面
     * <p>
     * 创建并配置二级缓存AOP切面，用于拦截@TlCache注解标记的方法
     *
     * @param lockStrategyFactory 锁策略工厂
     * @return 二级缓存切面实例
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnBean(LockStrategyFactory.class)
    public TlCacheAspect tlCacheAspect(LockStrategyFactory lockStrategyFactory,
                                       CacheProtectionManager cacheProtectionManager,
                                       AsyncBloomFilterService asyncBloomFilterService) {
        log.info("初始化二级缓存切面");
        return new TlCacheAspect(lockStrategyFactory, cacheProtectionManager, asyncBloomFilterService);
    }

    /**
     * 注册布隆过滤器预热Runner
     * <p>
     * 创建并配置布隆过滤器预热Runner，用于在应用启动时预热布隆过滤器
     *
     * @param asyncBloomFilterService 异步加载布隆过滤器服务
     * @param warmupProperties        预热配置属性
     * @param jdbcTemplate            JDBC模板
     * @return 布隆过滤器预热Runner实例
     */
    @Bean
    @ConditionalOnProperty(prefix = "tl-cache.bloom-filter-warmup", name = "enabled", havingValue = "true")
    @ConditionalOnBean({BloomFilterManager.class, JdbcTemplate.class, AsyncBloomFilterService.class})
    @ConditionalOnMissingBean
    public BloomFilterWarmupRunner bloomFilterWarmupRunner(AsyncBloomFilterService asyncBloomFilterService,
                                                           BloomFilterWarmupProperties warmupProperties,
                                                           JdbcTemplate jdbcTemplate) {
        log.info("注册布隆过滤器预热Runner");
        return new BloomFilterWarmupRunner(asyncBloomFilterService, warmupProperties, jdbcTemplate);
    }
}
