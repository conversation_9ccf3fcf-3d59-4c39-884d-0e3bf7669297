package org.dromara.common.tlcache.example;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.tlcache.annotation.BloomFilter;
import org.dromara.common.tlcache.annotation.RandomExpire;
import org.dromara.common.tlcache.annotation.TlCache;
import org.dromara.common.tlcache.enums.RandomStrategy;
import org.springframework.stereotype.Service;

/**
 * 缓存防护功能使用示例
 * <p>
 * 展示如何使用缓存防护机制：
 * - 布隆过滤器防穿透
 * - 随机过期防雪崩
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Slf4j
public class CacheProtectionExampleService {

    /**
     * 布隆过滤器防穿透示例
     * <p>
     * 适用场景：防止恶意查询不存在的数据，避免缓存穿透
     */
    @TlCache(
        cacheName = "userInfo",
        key = "#userId",
        l1ExpireSeconds = 300,
        l2ExpireSeconds = 1800,
        bloomFilter = @BloomFilter(
            enabled = true,
            expectedInsertions = 1000000,  // 预期100万用户
            fpp = 0.01,                    // 1%误判率
            autoInit = true               // 自动初始化
        )
    )
    public UserInfo getUserInfo(Long userId) {
        log.info("从数据库查询用户信息: {}", userId);
        // 模拟数据库查询
        if (userId <= 0 || userId > 1000000) {
            // 用户不存在
            return null;
        }
        return new UserInfo(userId, "用户" + userId, "user" + userId + "@example.com");
    }

    /**
     * 随机过期防雪崩示例
     * <p>
     * 适用场景：防止大量缓存同时过期导致的缓存雪崩
     */
    @TlCache(
        cacheName = "productInfo",
        key = "#productId",
        l1ExpireSeconds = 600,
        l2ExpireSeconds = 3600,
        randomExpire = @RandomExpire(
            enabled = true,
            minSeconds = 0,                // 随机范围最小值
            maxSeconds = 600,              // 随机范围最大值（10分钟）
            strategy = RandomStrategy.UNIFORM,
            applyToL1 = false              // 仅对L2缓存应用随机过期
        )
    )
    public ProductInfo getProductInfo(Long productId) {
        log.info("从数据库查询商品信息: {}", productId);
        return new ProductInfo(productId, "商品" + productId, 99.99 + productId);
    }

    /**
     * 综合防护示例
     * <p>
     * 同时启用布隆过滤器和随机过期防护机制
     */
    @TlCache(
        cacheName = "criticalData",
        key = "#dataId",
        l1ExpireSeconds = 600,
        l2ExpireSeconds = 3600,
        bloomFilter = @BloomFilter(
            enabled = true,
            expectedInsertions = 500000,
            fpp = 0.005                    // 更低的误判率
        ),
        randomExpire = @RandomExpire(
            enabled = true,
            minSeconds = 0,
            maxSeconds = 300,
            strategy = RandomStrategy.NORMAL
        )
    )
    public CriticalData getCriticalData(String dataId) {
        log.info("查询关键数据: {}", dataId);
        return new CriticalData(dataId, "关键数据" + dataId, "重要信息", System.currentTimeMillis());
    }

    // 示例数据类
    public record UserInfo(Long userId, String userName, String email) {
    }

    public record ProductInfo(Long productId, String productName, Double price) {
    }

    public record CriticalData(String dataId, String dataName, String details, Long timestamp) {
    }
}
