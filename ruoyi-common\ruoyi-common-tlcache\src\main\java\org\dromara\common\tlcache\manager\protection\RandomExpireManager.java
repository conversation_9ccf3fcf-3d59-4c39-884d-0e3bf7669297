package org.dromara.common.tlcache.manager.protection;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.tlcache.config.protection.RandomExpireConfig;
import org.dromara.common.tlcache.enums.RandomStrategy;
import org.springframework.stereotype.Component;

import java.util.concurrent.ThreadLocalRandom;

/**
 * 随机过期管理器
 * <p>
 * 负责计算随机过期时间，防止缓存雪崩
 * 通过在基础过期时间上增加随机时间，避免大量缓存同时过期
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Component
@Slf4j
public class RandomExpireManager {

    /**
     * 计算L1缓存的随机过期时间
     *
     * @param baseExpireSeconds 基础过期时间（秒）
     * @param config            随机过期配置
     * @return 加上随机时间后的过期时间
     */
    public long calculateL1RandomExpire(long baseExpireSeconds, RandomExpireConfig config) {
        if (!config.isEnabled() || !config.isApplyToL1()) {
            return baseExpireSeconds;
        }

        return calculateRandomExpire(baseExpireSeconds, config, "L1");
    }

    /**
     * 计算L2缓存的随机过期时间
     *
     * @param baseExpireSeconds 基础过期时间（秒）
     * @param config            随机过期配置
     * @return 加上随机时间后的过期时间
     */
    public long calculateL2RandomExpire(long baseExpireSeconds, RandomExpireConfig config) {
        if (!config.isEnabled()) {
            return baseExpireSeconds;
        }

        return calculateRandomExpire(baseExpireSeconds, config, "L2");
    }

    /**
     * 计算随机过期时间的核心方法
     *
     * @param baseExpireSeconds 基础过期时间（秒）
     * @param config            随机过期配置
     * @param cacheLevel        缓存级别（用于日志）
     * @return 加上随机时间后的过期时间
     */
    private long calculateRandomExpire(long baseExpireSeconds, RandomExpireConfig config, String cacheLevel) {
        if (config.getMinSeconds() == config.getMaxSeconds()) {
            // 如果最小值等于最大值，直接返回基础时间加上固定值
            long result = baseExpireSeconds + config.getMinSeconds();
            log.debug("{}缓存固定随机过期: base={}s, random={}s, result={}s",
                cacheLevel, baseExpireSeconds, config.getMinSeconds(), result);
            return result;
        }

        long randomSeconds = generateRandomSeconds(config);
        long result = baseExpireSeconds + randomSeconds;

        log.debug("{}缓存随机过期: base={}s, random={}s, result={}s, strategy={}",
            cacheLevel, baseExpireSeconds, randomSeconds, result, config.getStrategy());

        return result;
    }

    /**
     * 生成随机秒数
     *
     * @param config 随机过期配置
     * @return 随机秒数
     */
    private long generateRandomSeconds(RandomExpireConfig config) {
        if (config.getMinSeconds() >= config.getMaxSeconds()) {
            return config.getMinSeconds();
        }

        return switch (config.getStrategy()) {
            case UNIFORM -> generateUniformRandom(config);
            case NORMAL -> generateNormalRandom(config);
            case EXPONENTIAL -> generateExponentialRandom(config);
        };
    }

    /**
     * 生成均匀分布随机数
     *
     * @param config 随机过期配置
     * @return 随机秒数
     */
    private long generateUniformRandom(RandomExpireConfig config) {
        ThreadLocalRandom random = ThreadLocalRandom.current();
        return random.nextLong(config.getMinSeconds(), config.getMaxSeconds() + 1);
    }

    /**
     * 生成正态分布随机数
     *
     * @param config 随机过期配置
     * @return 随机秒数
     */
    private long generateNormalRandom(RandomExpireConfig config) {
        ThreadLocalRandom random = ThreadLocalRandom.current();

        double mean = (config.getMinSeconds() + config.getMaxSeconds()) / 2.0;
        double stdDev = (config.getMaxSeconds() - config.getMinSeconds()) / 6.0; // 99.7%的值在3σ范围内

        double value = random.nextGaussian() * stdDev + mean;

        // 确保值在有效范围内
        value = Math.max(config.getMinSeconds(), Math.min(config.getMaxSeconds(), value));
        return (long) value;
    }

    /**
     * 生成指数分布随机数
     *
     * @param config 随机过期配置
     * @return 随机秒数
     */
    private long generateExponentialRandom(RandomExpireConfig config) {
        ThreadLocalRandom random = ThreadLocalRandom.current();

        // 调整lambda参数使大部分值在范围内
        double lambda = 2.0 / (config.getMaxSeconds() - config.getMinSeconds());
        double value = -Math.log(1 - random.nextDouble()) / lambda + config.getMinSeconds();

        // 确保值在有效范围内
        value = Math.min(config.getMaxSeconds(), value);
        return (long) value;
    }

    /**
     * 批量计算随机过期时间
     * <p>
     * 用于批量操作时计算多个key的过期时间
     *
     * @param baseExpireSeconds 基础过期时间（秒）
     * @param config            随机过期配置
     * @param count             需要计算的数量
     * @param isL1Cache         是否为L1缓存
     * @return 随机过期时间数组
     */
    public long[] batchCalculateRandomExpire(long baseExpireSeconds, RandomExpireConfig config,
                                             int count, boolean isL1Cache) {
        if (!config.isEnabled() || (isL1Cache && !config.isApplyToL1())) {
            // 如果未启用随机过期，返回相同的基础过期时间
            long[] result = new long[count];
            for (int i = 0; i < count; i++) {
                result[i] = baseExpireSeconds;
            }
            return result;
        }

        long[] result = new long[count];
        String cacheLevel = isL1Cache ? "L1" : "L2";

        for (int i = 0; i < count; i++) {
            result[i] = calculateRandomExpire(baseExpireSeconds, config, cacheLevel);
        }

        log.debug("批量计算{}缓存随机过期时间: count={}, base={}s, range=[{}s, {}s]",
            cacheLevel, count, baseExpireSeconds, config.getMinSeconds(), config.getMaxSeconds());

        return result;
    }

    /**
     * 获取随机过期的统计信息
     *
     * @param config 随机过期配置
     * @return 统计信息
     */
    public RandomExpireStats getStats(RandomExpireConfig config) {
        return RandomExpireStats.builder()
            .enabled(config.isEnabled())
            .strategy(config.getStrategy())
            .minSeconds(config.getMinSeconds())
            .maxSeconds(config.getMaxSeconds())
            .applyToL1(config.isApplyToL1())
            .averageRandomSeconds((config.getMinSeconds() + config.getMaxSeconds()) / 2.0)
            .build();
    }

    /**
     * 验证随机过期配置
     *
     * @param config 随机过期配置
     * @throws IllegalArgumentException 如果配置无效
     */
    public void validateConfig(RandomExpireConfig config) {
        try {
            config.validate();
        } catch (IllegalArgumentException e) {
            log.error("随机过期配置验证失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 预热随机数生成器
     * <p>
     * 在应用启动时调用，预热随机数生成器以提高性能
     */
    public void warmupRandomGenerator() {
        ThreadLocalRandom random = ThreadLocalRandom.current();
        // 生成一些随机数来预热
        for (int i = 0; i < 100; i++) {
            random.nextLong(0, 1000);
            random.nextGaussian();
            random.nextDouble();
        }
        log.debug("随机数生成器预热完成");
    }

    /**
     * 随机过期统计信息
     */
    public static class RandomExpireStats {
        private boolean enabled;
        private RandomStrategy strategy;
        private long minSeconds;
        private long maxSeconds;
        private boolean applyToL1;
        private double averageRandomSeconds;

        public static RandomExpireStatsBuilder builder() {
            return new RandomExpireStatsBuilder();
        }

        // Getters
        public boolean isEnabled() {
            return enabled;
        }

        public RandomStrategy getStrategy() {
            return strategy;
        }

        public long getMinSeconds() {
            return minSeconds;
        }

        public long getMaxSeconds() {
            return maxSeconds;
        }

        public boolean isApplyToL1() {
            return applyToL1;
        }

        public double getAverageRandomSeconds() {
            return averageRandomSeconds;
        }

        public static class RandomExpireStatsBuilder {
            private final RandomExpireStats stats = new RandomExpireStats();

            public RandomExpireStatsBuilder enabled(boolean enabled) {
                stats.enabled = enabled;
                return this;
            }

            public RandomExpireStatsBuilder strategy(RandomStrategy strategy) {
                stats.strategy = strategy;
                return this;
            }

            public RandomExpireStatsBuilder minSeconds(long minSeconds) {
                stats.minSeconds = minSeconds;
                return this;
            }

            public RandomExpireStatsBuilder maxSeconds(long maxSeconds) {
                stats.maxSeconds = maxSeconds;
                return this;
            }

            public RandomExpireStatsBuilder applyToL1(boolean applyToL1) {
                stats.applyToL1 = applyToL1;
                return this;
            }

            public RandomExpireStatsBuilder averageRandomSeconds(double averageRandomSeconds) {
                stats.averageRandomSeconds = averageRandomSeconds;
                return this;
            }

            public RandomExpireStats build() {
                return stats;
            }
        }
    }
}
