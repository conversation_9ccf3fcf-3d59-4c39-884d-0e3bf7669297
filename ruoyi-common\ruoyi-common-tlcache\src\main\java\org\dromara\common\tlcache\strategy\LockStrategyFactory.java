package org.dromara.common.tlcache.strategy;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.tlcache.config.properties.TlCacheConfigProperties;
import org.dromara.common.tlcache.enums.LockStrategyType;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

/**
 * 锁策略工厂
 * 根据配置选择不同的锁策略实现
 * <p>
 * 该工厂根据配置属性动态选择合适的锁策略实现：
 * - SYNCHRONIZED: 适用于一般并发场景，使用synchronized关键字和Redisson分布式锁
 * - COMPLETABLE_FUTURE: 适用于高并发场景，使用CompletableFuture和Redisson分布式锁
 * <p>
 * 通过在配置文件中设置tl-cache.lock-strategy属性来切换不同的锁策略
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Component
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(prefix = "tl-cache", name = "enabled", havingValue = "true", matchIfMissing = true)
public class LockStrategyFactory {

    private final SynchronizedLockStrategy synchronizedLockStrategy;
    private final CompletableFutureLockStrategy completableFutureLockStrategy;
    private final StripedLockStrategy stripedLockStrategy;
    private final TlCacheConfigProperties configProperties;

    /**
     * 获取当前配置的锁策略
     * <p>
     * 根据配置属性返回相应的锁策略实现
     * 如果配置的策略类型无效，则默认使用SYNCHRONIZED策略
     *
     * @return 锁策略实现实例
     */
    public CacheLockStrategy getLockStrategy() {
        LockStrategyType strategyType = configProperties.getLockStrategy();
        return getLockStrategy(strategyType);
    }

    /**
     * 根据指定的策略类型获取锁策略
     * <p>
     * 支持方法级别的锁策略选择，如果传入AUTO则使用全局配置
     *
     * @param strategyType 锁策略类型
     * @return 锁策略实现实例
     */
    public CacheLockStrategy getLockStrategy(LockStrategyType strategyType) {
        // 如果是AUTO，使用全局配置的策略
        if (strategyType == LockStrategyType.AUTO) {
            strategyType = configProperties.getLockStrategy();
        }

        log.debug("使用锁策略: {}", strategyType);

        return switch (strategyType) {
            case AUTO -> synchronizedLockStrategy; // 兜底，理论上不会到这里
            case SYNCHRONIZED -> synchronizedLockStrategy;
            case COMPLETABLE_FUTURE -> completableFutureLockStrategy;
            case STRIPED -> stripedLockStrategy;
        };
    }
}
