package org.dromara.common.tlcache.strategy;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 缓存操作结果封装类
 * <p>
 * 用于封装缓存操作的结果和元信息，支持布隆过滤器优化
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CacheResult {

    /**
     * 缓存值
     */
    private Object value;

    /**
     * 是否缓存命中
     */
    private boolean cacheHit;

    /**
     * 缓存命中层级
     */
    private CacheHitLevel hitLevel;

    /**
     * 是否需要布隆过滤器检查
     * <p>
     * 当缓存命中时，通常不需要布隆过滤器检查
     */
    private boolean needBloomFilterCheck;

    /**
     * 创建缓存命中结果
     *
     * @param value    缓存值
     * @param hitLevel 命中层级
     * @return 缓存结果
     */
    public static CacheResult hit(Object value, CacheHitLevel hitLevel) {
        return new CacheResult(value, true, hitLevel, false);
    }

    /**
     * 创建L1缓存命中结果
     *
     * @param value 缓存值
     * @return 缓存结果
     */
    public static CacheResult hitL1(Object value) {
        return hit(value, CacheHitLevel.L1);
    }

    /**
     * 创建L2缓存命中结果
     *
     * @param value 缓存值
     * @return 缓存结果
     */
    public static CacheResult hitL2(Object value) {
        return hit(value, CacheHitLevel.L2);
    }

    /**
     * 创建缓存未命中结果
     *
     * @param value 加载的值
     * @return 缓存结果
     */
    public static CacheResult miss(Object value) {
        return new CacheResult(value, false, CacheHitLevel.MISS, true);
    }

    /**
     * 创建需要布隆过滤器检查的未命中结果
     *
     * @return 缓存结果
     */
    public static CacheResult missWithBloomFilterCheck() {
        return new CacheResult(null, false, CacheHitLevel.MISS, true);
    }

    /**
     * 解包缓存值，处理NULL_VALUE标记
     *
     * @return 解包后的值
     */
    public Object unwrapValue() {
        return value == CacheLockStrategy.NULL_VALUE ? null : value;
    }

    /**
     * 是否为空值
     *
     * @return true表示空值
     */
    public boolean isNullValue() {
        return value == CacheLockStrategy.NULL_VALUE;
    }

    /**
     * 缓存命中层级枚举
     */
    public enum CacheHitLevel {
        /**
         * L1缓存命中（本地缓存）
         */
        L1,
        /**
         * L2缓存命中（Redis缓存）
         */
        L2,
        /**
         * 缓存未命中，需要加载数据
         */
        MISS
    }
}
