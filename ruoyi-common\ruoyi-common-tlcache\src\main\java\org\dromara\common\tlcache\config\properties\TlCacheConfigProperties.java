package org.dromara.common.tlcache.config.properties;

import lombok.Data;
import org.dromara.common.tlcache.enums.LockStrategyType;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 二级缓存配置属性
 * <p>
 * 用于配置二级缓存系统的全局参数，包括：
 * - 是否启用缓存
 * - Redis键前缀
 * - L1缓存最大条目数
 * - 锁策略类型
 * <p>
 * 这些配置可在application.yml中通过tl-cache前缀进行设置
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Data
@ConfigurationProperties(prefix = "tl-cache")
public class TlCacheConfigProperties {
    /**
     * 是否启用二级缓存，默认为true
     */
    private boolean enabled = true;

    /**
     * 是否启用一级缓存（Caffeine本地缓存），默认为true
     * <p>
     * 当设置为false时，系统将跳过L1缓存，直接使用L2缓存（Redis）
     * 适用场景：
     * - 内存资源紧张的环境
     * - 需要强制所有缓存都走分布式缓存的场景
     * - 调试和测试场景
     */
    private boolean enableL1Cache = true;

    /**
     * Redis Key的全局前缀，用于防止多应用间缓存键冲突
     */
    private String redisKeyPrefix = "tl-cache";

    /**
     * L1缓存最大条目数，默认10000
     * 注意：当enableL1Cache为false时，此配置无效
     */
    private int l1MaxSize = 10000;

    /**
     * 锁策略类型
     */
    private LockStrategyType lockStrategy = LockStrategyType.SYNCHRONIZED;

    /**
     * 锁相关配置
     */
    private LockConfig lock = new LockConfig();

    /**
     * 锁配置类
     */
    @Data
    public static class LockConfig {
        /**
         * 分布式锁等待超时时间（秒），默认3秒
         */
        private long waitTimeout = 3;

        /**
         * 分布式锁过期时间（秒），默认30秒
         */
        private long leaseTimeout = 30;

        /**
         * 锁获取失败后的重试间隔（毫秒），默认100毫秒
         */
        private long retryInterval = 100;

        /**
         * 最大重试次数，默认3次
         */
        private int maxRetries = 3;

        /**
         * CompletableFuture等待超时时间（秒），默认5秒
         */
        private int futureTimeout = 5;

        /**
         * 是否启用锁降级，当分布式锁不可用时降级为本地锁，默认true
         */
        private boolean enableFallback = true;
    }


}
