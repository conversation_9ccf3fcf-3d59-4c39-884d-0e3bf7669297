package org.dromara.common.tlcache.runner;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.tlcache.config.properties.BloomFilterWarmupProperties;
import org.dromara.common.tlcache.service.AsyncBloomFilterService;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

/**
 * 布隆过滤器预热Runner
 * <p>
 * 基于配置文件自动预热布隆过滤器，支持多个布隆过滤器的异步初始化
 * 在应用启动时执行，将配置的数据异步加载到布隆过滤器中，不阻塞应用启动
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Component
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(prefix = "tl-cache.bloom-filter-warmup", name = "enabled", havingValue = "true")
public class BloomFilterWarmupRunner implements ApplicationRunner {

    private final AsyncBloomFilterService asyncBloomFilterService;
    private final BloomFilterWarmupProperties warmupProperties;
    private final JdbcTemplate jdbcTemplate;

    @Override
    public void run(ApplicationArguments args) {
        try {
            // 验证配置
            warmupProperties.validate();

            if (warmupProperties.getConfigs().isEmpty()) {
                log.info("布隆过滤器预热配置为空，跳过预热");
                return;
            }

            log.info("启动布隆过滤器异步预热，共{}个配置", warmupProperties.getConfigs().size());

            // 启动异步预热任务，不阻塞应用启动
            asyncBloomFilterService.asyncWarmupAllBloomFilters(warmupProperties.getConfigs(), jdbcTemplate);

            log.info("布隆过滤器异步预热任务已启动，预热将在后台进行");

        } catch (Exception e) {
            log.error("启动布隆过滤器异步预热失败", e);
        }
    }

}
