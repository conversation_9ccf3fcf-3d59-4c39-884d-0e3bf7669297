package org.dromara.common.tlcache.aspect;

import cn.hutool.core.util.ArrayUtil;
import jakarta.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.tlcache.annotation.TlCache;
import org.dromara.common.tlcache.config.protection.BloomFilterConfig;
import org.dromara.common.tlcache.config.protection.RandomExpireConfig;
import org.dromara.common.tlcache.enums.LockStrategyType;
import org.dromara.common.tlcache.manager.protection.CacheProtectionManager;
import org.dromara.common.tlcache.service.AsyncBloomFilterService;
import org.dromara.common.tlcache.strategy.CacheLockStrategy;
import org.dromara.common.tlcache.strategy.CacheResult;
import org.dromara.common.tlcache.strategy.LockStrategyFactory;
import org.dromara.common.tlcache.util.CacheExceptionHandler;
import org.dromara.common.tlcache.util.ProtectionConfigConverter;
import org.dromara.common.tlcache.util.SpelExpressionCache;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 二级缓存AOP处理
 * <p>
 * 该切面拦截标记有@TlCache注解的方法，实现缓存逻辑：
 * 1. 解析SpEL表达式生成缓存键
 * 2. 尝试从缓存获取数据
 * 3. 缓存未命中时执行目标方法并缓存结果
 * 4. 根据配置的锁策略处理并发情况
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(prefix = "tl-cache", name = "enabled", havingValue = "true", matchIfMissing = true)
public class TlCacheAspect {

    private final LockStrategyFactory lockStrategyFactory;
    private final CacheProtectionManager cacheProtectionManager;
    private final AsyncBloomFilterService asyncBloomFilterService;
    private final DefaultParameterNameDiscoverer discoverer = new DefaultParameterNameDiscoverer();

    /**
     * 方法缓存，提高性能
     * 缓存已解析的Method对象，避免重复反射获取，提升性能
     */
    private final Map<String, Method> methodCache = new ConcurrentHashMap<>();

    /**
     * 方法锁策略缓存，避免重复解析注解
     * 键为方法的完整名称，值为对应的锁策略实例
     */
    private final Map<String, CacheLockStrategy> methodStrategyCache = new ConcurrentHashMap<>();

    /**
     * 缓存清理，防止内存泄漏
     * <p>
     * 在应用关闭时清理缓存，释放内存资源
     */
    @PreDestroy
    public void cleanup() {
        log.info("清理TlCacheAspect缓存资源");
        methodCache.clear();
        methodStrategyCache.clear();
        SpelExpressionCache.clearAll();
    }

    /**
     * 环绕通知处理二级缓存逻辑
     * <p>
     * 拦截标记有@TlCache注解的方法，实现缓存逻辑：
     * 1. 解析缓存名称和键
     * 2. 根据注解中的锁策略选择对应的锁实现
     * 3. 委托给锁策略处理缓存获取和加载
     *
     * @param joinPoint 切点
     * @param tlCache   缓存注解
     * @return 缓存的结果或方法执行结果
     * @throws Throwable 执行过程中的异常
     */
    @Around("@annotation(tlCache)")
    public Object around(ProceedingJoinPoint joinPoint, TlCache tlCache) throws Throwable {
        String cacheName = tlCache.cacheName();
        if (StringUtils.isEmpty(cacheName)) {
            log.warn("TwoLevelCache注解的cacheName不能为空");
            return joinPoint.proceed();
        }

        String key = parseKey(tlCache.key(), joinPoint);
        if (StringUtils.isEmpty(key)) {
            log.warn("缓存键解析为空，直接调用方法. cacheName: {}", cacheName);
            return joinPoint.proceed();
        }

        // 转换防护配置
        BloomFilterConfig bloomFilterConfig = ProtectionConfigConverter.convert(tlCache.bloomFilter());
        RandomExpireConfig randomExpireConfig = ProtectionConfigConverter.convert(tlCache.randomExpire());

        // 获取方法对应的锁策略
        CacheLockStrategy lockStrategy = getMethodLockStrategy(joinPoint, tlCache);

        // 计算过期时间（应用随机过期）
        long l1ExpireSeconds = tlCache.l1ExpireSeconds();
        long l2ExpireSeconds = tlCache.l2ExpireSeconds();

        if (randomExpireConfig != null && randomExpireConfig.isEnabled()) {
            CacheProtectionManager.ExpireTimeResult expireResult =
                cacheProtectionManager.calculateExpireTime(l1ExpireSeconds, l2ExpireSeconds, randomExpireConfig);

            if (!expireResult.hasError()) {
                l1ExpireSeconds = expireResult.getL1ExpireSeconds();
                l2ExpireSeconds = expireResult.getL2ExpireSeconds();
                log.debug("应用随机过期时间: cacheName={}, key={}, l1={}, l2={}",
                    cacheName, key, l1ExpireSeconds, l2ExpireSeconds);
            }
        }

        // 只有在缓存未命中时才进行布隆过滤器检查
        try {
            CacheResult cacheResult = lockStrategy.getOrLoadWithMetadata(
                cacheName,
                key,
                l1ExpireSeconds,
                () -> {
                    try {
                        // 只有在缓存未命中时才进行布隆过滤器检查
                        if (bloomFilterConfig != null && bloomFilterConfig.isEnabled()) {
                            CacheProtectionManager.AccessCheckResult checkResult =
                                cacheProtectionManager.checkAccess(cacheName, key, bloomFilterConfig);

                            if (!checkResult.isAllowed()) {
                                log.debug("缓存访问被拦截: cacheName={}, key={}, reason={}",
                                    cacheName, key, checkResult.getReason());

                                // 布隆过滤器拦截，返回null表示数据不存在
                                return null;
                            }
                        }

                        // 使用统一的异常处理逻辑
                        return CacheExceptionHandler.executeWithExceptionHandling(
                            getMethodKey(joinPoint), cacheName, key, () -> {
                                try {
                                    Object value = joinPoint.proceed();

                                    // 如果结果不为null且启用了布隆过滤器，异步添加到布隆过滤器
                                    if (value != null && bloomFilterConfig != null && bloomFilterConfig.isEnabled()) {
                                        asyncBloomFilterService.asyncAddToBloomFilter(cacheName, key, bloomFilterConfig);
                                    }

                                    return value;
                                } catch (Throwable e) {
                                    throw new RuntimeException(e);
                                }
                            });
                    } catch (Throwable e) {
                        throw new RuntimeException("缓存方法执行失败", e);
                    }
                },
                l2ExpireSeconds
            );

            return cacheResult.unwrapValue();
        } catch (Exception e) {
            // 如果优化的方法不可用，降级到原有逻辑
            log.debug("使用优化缓存逻辑失败，降级到原有逻辑: cacheName={}, key={}", cacheName, key, e);
            return fallbackToOriginalLogic(joinPoint, tlCache, cacheName, key, l1ExpireSeconds, l2ExpireSeconds,
                bloomFilterConfig, lockStrategy);
        }
    }

    /**
     * 降级到原有逻辑
     * <p>
     * 当优化的缓存逻辑不可用时，使用原有的逻辑作为兜底
     */
    private Object fallbackToOriginalLogic(ProceedingJoinPoint joinPoint, TlCache tlCache,
                                           String cacheName, String key, long l1ExpireSeconds, long l2ExpireSeconds,
                                           BloomFilterConfig bloomFilterConfig, CacheLockStrategy lockStrategy) throws Throwable {
        // 布隆过滤器检查
        if (bloomFilterConfig != null && bloomFilterConfig.isEnabled()) {
            CacheProtectionManager.AccessCheckResult checkResult =
                cacheProtectionManager.checkAccess(cacheName, key, bloomFilterConfig);

            if (!checkResult.isAllowed()) {
                log.debug("缓存访问被拦截: cacheName={}, key={}, reason={}",
                    cacheName, key, checkResult.getReason());
                // 布隆过滤器拦截，返回null表示数据不存在
                return null;
            }
        }

        // 使用原有的锁策略执行缓存逻辑
        return lockStrategy.getOrLoad(
            cacheName,
            key,
            l1ExpireSeconds,
            () -> CacheExceptionHandler.executeWithExceptionHandling(
                getMethodKey(joinPoint), cacheName, key, () -> {
                    try {
                        Object value = joinPoint.proceed();

                        // 如果结果不为null且启用了布隆过滤器，同步添加到布隆过滤器
                        if (value != null && bloomFilterConfig != null && bloomFilterConfig.isEnabled()) {
                            asyncBloomFilterService.syncAddToBloomFilter(cacheName, key, bloomFilterConfig);
                        }

                        return value;
                    } catch (Throwable e) {
                        throw new RuntimeException(e);
                    }
                }),
            l2ExpireSeconds
        );
    }

    /**
     * 获取方法对应的锁策略
     * <p>
     * 根据注解中的锁策略类型选择对应的实现，并缓存结果避免重复解析
     *
     * @param joinPoint 切点
     * @param tlCache   缓存注解
     * @return 锁策略实例
     */
    private CacheLockStrategy getMethodLockStrategy(ProceedingJoinPoint joinPoint, TlCache tlCache) {
        String methodKey = getMethodKey(joinPoint);
        return methodStrategyCache.computeIfAbsent(methodKey, k -> {
            LockStrategyType strategyType = tlCache.lockStrategy();
            log.debug("方法 {} 使用锁策略: {}", methodKey, strategyType);
            return lockStrategyFactory.getLockStrategy(strategyType);
        });
    }

    /**
     * 生成方法的唯一标识键
     *
     * @param joinPoint 切点
     * @return 方法的唯一标识
     */
    private String getMethodKey(ProceedingJoinPoint joinPoint) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        return methodSignature.getDeclaringTypeName() + "." + methodSignature.getName();
    }

    /**
     * 解析SpEL表达式生成缓存键
     * <p>
     * 使用Spring的表达式解析器，将SpEL表达式解析为实际的缓存键值
     * 支持从方法参数中获取值，例如 #id 或 #user.name
     *
     * @param keyExpression SpEL表达式
     * @param joinPoint     切点
     * @return 解析后的缓存键，如果解析失败则返回null
     */
    private String parseKey(String keyExpression, ProceedingJoinPoint joinPoint) {
        if (StringUtils.isEmpty(keyExpression)) {
            return null;
        }

        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        String methodFullName = getMethodKey(joinPoint);

        // 从缓存获取方法，避免反复获取Method对象
        Method method = methodCache.computeIfAbsent(methodFullName, k -> {
            try {
                return joinPoint.getTarget().getClass().getDeclaredMethod(methodSignature.getName(),
                    methodSignature.getParameterTypes());
            } catch (NoSuchMethodException e) {
                log.error("获取方法对象失败", e);
                return methodSignature.getMethod();
            }
        });

        Object[] args = joinPoint.getArgs();

        // 如果没有参数，无法使用SpEL解析
        if (ArrayUtil.isEmpty(args)) {
            return keyExpression;
        }

        // 获取方法参数名
        String[] paramNames = discoverer.getParameterNames(method);
        if (paramNames == null || paramNames.length == 0) {
            log.warn("无法获取方法参数名，无法解析SpEL表达式: {}", keyExpression);
            return null;
        }

        // 构建SpEL上下文
        EvaluationContext context = new StandardEvaluationContext();
        for (int i = 0; i < paramNames.length; i++) {
            context.setVariable(paramNames[i], args[i]);
        }

        try {
            Expression expression = SpelExpressionCache.getOrParseExpression(keyExpression);
            return expression.getValue(context, String.class);
        } catch (Exception e) {
            log.error("解析SpEL表达式出错: {} - {}", keyExpression, e.getMessage());
            return null;
        }
    }
}
