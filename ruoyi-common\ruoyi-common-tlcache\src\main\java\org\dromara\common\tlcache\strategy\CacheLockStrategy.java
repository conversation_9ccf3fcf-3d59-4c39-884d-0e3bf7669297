package org.dromara.common.tlcache.strategy;

import java.util.function.Supplier;

/**
 * 缓存锁策略接口
 * 定义不同锁实现的规范
 * <p>
 * 该接口定义了缓存锁策略的核心方法，用于处理缓存的获取和加载逻辑。
 * 不同的实现类可以提供不同的锁机制，以适应不同的并发场景：
 * - SynchronizedLockStrategy: 基于synchronized的简单锁策略
 * - CompletableFutureLockStrategy: 基于CompletableFuture的高并发锁策略
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
public interface CacheLockStrategy {

    /**
     * 表示空值的标记对象，用于缓存空结果
     * <p>
     * 当方法返回null时，使用此标记对象存储在缓存中，避免缓存穿透问题
     * （缓存穿透：大量请求查询不存在的数据，导致请求都落到数据库上）
     */
    Object NULL_VALUE = new Object();

    /**
     * 获取缓存值，如果不存在则加载
     *
     * @param cacheName       缓存名称
     * @param key             缓存键
     * @param l1ExpireSeconds L1缓存过期时间
     * @param valueLoader     值加载器，用于在缓存未命中时加载数据
     * @param l2ExpireSeconds L2缓存过期时间
     * @return 缓存值或加载的值
     * @throws Throwable 加载过程中可能抛出的异常
     */
    Object getOrLoad(String cacheName, String key, long l1ExpireSeconds,
                     Supplier<Object> valueLoader, long l2ExpireSeconds) throws Throwable;

    /**
     * 获取缓存值，如果不存在则加载（带缓存命中信息）
     * <p>
     * 此方法返回详细的缓存操作结果，包括是否命中缓存、命中层级等信息，
     * 用于支持布隆过滤器优化等高级功能
     *
     * @param cacheName       缓存名称
     * @param key             缓存键
     * @param l1ExpireSeconds L1缓存过期时间
     * @param valueLoader     值加载器，用于在缓存未命中时加载数据
     * @param l2ExpireSeconds L2缓存过期时间
     * @return 缓存操作结果
     * @throws Throwable 加载过程中可能抛出的异常
     */
    default CacheResult getOrLoadWithMetadata(String cacheName, String key, long l1ExpireSeconds,
                                              Supplier<Object> valueLoader, long l2ExpireSeconds) throws Throwable {
        // 默认实现：调用原有方法并包装结果
        Object result = getOrLoad(cacheName, key, l1ExpireSeconds, valueLoader, l2ExpireSeconds);
        // 由于无法确定是否命中缓存，默认认为需要布隆过滤器检查，并返回加载的结果
        return CacheResult.miss(result);
    }

    /**
     * 解包缓存值，处理NULL_VALUE标记
     * <p>
     * 将缓存中的值转换为实际返回值，如果缓存值是NULL_VALUE标记，则返回null
     * 这样可以在缓存层面处理空值，而对调用方保持透明
     *
     * @param cacheValue 缓存值
     * @return 解包后的值，如果是NULL_VALUE则返回null
     */
    default Object unwrapCacheValue(Object cacheValue) {
        return cacheValue == NULL_VALUE ? null : cacheValue;
    }
}
