# 二级缓存配置示例
tl-cache:
  # 是否启用二级缓存，默认为true
  enabled: true

  # 是否启用一级缓存（Caffeine本地缓存），默认为true
  # 设置为false时，系统将跳过L1缓存，直接使用L2缓存（Redis）
  # 适用场景：内存资源紧张、需要强制分布式缓存、调试测试等
  enable-l1-cache: true

  # Redis Key的全局前缀，用于防止多应用间缓存键冲突
  redis-key-prefix: tl-cache

  # L1缓存最大条目数，默认10000
  # 注意：当enable-l1-cache为false时，此配置无效
  l1-max-size: 10000

  # 锁策略类型：SYNCHRONIZED、COMPLETABLE_FUTURE、STRIPED(默认)
  lock-strategy: SYNCHRONIZED

  # 锁相关配置
  lock:
    # 分布式锁等待超时时间（秒），默认3秒
    wait-timeout: 3

    # 分布式锁过期时间（秒），默认30秒
    lease-timeout: 30

    # 锁获取失败后的重试间隔（毫秒），默认100毫秒
    retry-interval: 100

    # 最大重试次数，默认3次
    max-retries: 3

    # CompletableFuture等待超时时间（秒），默认5秒
    future-timeout: 5

    # 是否启用锁降级，当分布式锁不可用时降级为本地锁，默认true
    enable-fallback: true

# 使用示例：

# 1. 基础用法 - 使用全局配置的锁策略
# @TlCache(cacheName = "user", key = "#id", l1ExpireSeconds = 300, l2ExpireSeconds = 3600)
# public User getUserById(Long id) {
#     return userMapper.selectById(id);
# }

# 2. 高并发热点数据 - 使用分段锁策略
# import org.dromara.common.tlcache.enums.LockStrategyType;
# @TlCache(
#     cacheName = "hotUser",
#     key = "#id",
#     lockStrategy = LockStrategyType.STRIPED,
#     l1ExpireSeconds = 60,
#     l2ExpireSeconds = 300
# )
# public User getHotUser(Long id) {
#     return userMapper.selectById(id);
# }

# 3. 重要配置数据 - 使用同步锁策略
# @TlCache(
#     cacheName = "config",
#     key = "#key",
#     lockStrategy = LockStrategyType.SYNCHRONIZED,
#     l1ExpireSeconds = 3600,
#     l2ExpireSeconds = 86400
# )
# public Config getConfig(String key) {
#     return configMapper.selectByKey(key);
# }

# 4. 统计类数据 - 使用CompletableFuture策略
# @TlCache(
#     cacheName = "stats",
#     key = "#userId",
#     lockStrategy = LockStrategyType.COMPLETABLE_FUTURE,
#     l1ExpireSeconds = 300,
#     l2ExpireSeconds = 1800
# )
# public UserStats getUserStats(Long userId) {
#     return statsService.calculateStats(userId);
# }

# 锁策略选择指南：

# 方法级别策略选择（推荐）：
# - AUTO: 使用全局配置，适用于大部分普通场景
# - STRIPED: 高并发热点数据，如用户信息、商品详情等
# - SYNCHRONIZED: 重要配置数据，需要强一致性保证
# - COMPLETABLE_FUTURE: 统计类数据，可容忍短暂不一致

# 全局策略配置：
# - SYNCHRONIZED: 适用于一般并发场景，实现简单，易于理解
# - COMPLETABLE_FUTURE: 适用于高并发场景，使用CompletableFuture减少分布式锁竞争
# - STRIPED: 适用于高并发场景，使用分段锁减少锁竞争，性能最佳

# 性能对比（高并发场景）：
# STRIPED > COMPLETABLE_FUTURE > SYNCHRONIZED

# 一致性保证：
# SYNCHRONIZED = STRIPED > COMPLETABLE_FUTURE

# 锁降级机制说明：
# 当enable-fallback=true时，如果分布式锁（Redis）不可用，系统会自动降级为仅使用本地锁
# 这样可以保证在Redis故障时，应用仍然可以正常运行，只是失去了分布式一致性保证

# L1缓存开关说明：

# 启用L1缓存（默认）：
# enable-l1-cache: true
# - 优势：访问速度快，减少网络开销，提高性能
# - 适用：大部分生产环境，内存充足的场景
# - 注意：需要考虑内存使用量和数据一致性

# 禁用L1缓存：
# enable-l1-cache: false
# - 优势：节省内存，强制分布式一致性，便于调试
# - 适用：内存紧张、多实例强一致性要求、测试环境
# - 注意：性能会有所下降，网络开销增加

# 使用场景建议：
# 1. 生产环境：通常启用L1缓存，提升性能
# 2. 内存受限环境：可禁用L1缓存，节省内存
# 3. 调试测试：可禁用L1缓存，简化缓存层次
# 4. 强一致性要求：可禁用L1缓存，避免本地缓存不一致

# 最佳实践：
# 1. 大部分方法使用 AUTO，让全局配置统一管理
# 2. 热点数据方法使用 STRIPED，提升并发性能
# 3. 重要配置使用 SYNCHRONIZED，保证数据一致性
# 4. 统计报表使用 COMPLETABLE_FUTURE，减少锁竞争
# 5. 根据内存情况和性能需求决定是否启用L1缓存
