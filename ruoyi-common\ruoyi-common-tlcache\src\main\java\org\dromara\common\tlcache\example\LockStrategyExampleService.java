package org.dromara.common.tlcache.example;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.tlcache.annotation.TlCache;
import org.dromara.common.tlcache.enums.LockStrategyType;
import org.springframework.stereotype.Service;

/**
 * 锁策略使用示例
 * <p>
 * 展示如何在不同场景下选择合适的锁策略
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Slf4j
public class LockStrategyExampleService {

    /**
     * 高并发热点数据 - 使用分段锁策略
     * <p>
     * 适用场景：
     * - 高频访问的用户信息
     * - 热门商品信息
     * - 系统配置等
     * <p>
     * 优势：分段锁减少锁竞争，提高并发性能
     */
    @TlCache(
        cacheName = "hotUser",
        key = "#userId",
        lockStrategy = LockStrategyType.STRIPED,
        l1ExpireSeconds = 60,    // 1分钟本地缓存
        l2ExpireSeconds = 300    // 5分钟分布式缓存
    )
    public UserInfo getHotUser(Long userId) {
        log.info("从数据库加载热点用户信息: {}", userId);
        // 模拟数据库查询
        return new UserInfo(userId, "热点用户" + userId, "<EMAIL>");
    }

    /**
     * 重要配置数据 - 使用同步锁策略
     * <p>
     * 适用场景：
     * - 系统配置
     * - 重要业务参数
     * - 需要强一致性的数据
     * <p>
     * 优势：实现简单，保证强一致性
     */
    @TlCache(
        cacheName = "systemConfig",
        key = "#configKey",
        lockStrategy = LockStrategyType.SYNCHRONIZED,
        l1ExpireSeconds = 3600,  // 1小时本地缓存
        l2ExpireSeconds = 86400  // 24小时分布式缓存
    )
    public SystemConfig getSystemConfig(String configKey) {
        log.info("从数据库加载系统配置: {}", configKey);
        // 模拟数据库查询
        return new SystemConfig(configKey, "配置值" + configKey, System.currentTimeMillis());
    }

    /**
     * 统计类数据 - 使用CompletableFuture策略
     * <p>
     * 适用场景：
     * - 用户统计信息
     * - 报表数据
     * - 可以容忍短暂不一致的数据
     * <p>
     * 优势：减少分布式锁竞争，适合高并发场景
     */
    @TlCache(
        cacheName = "userStats",
        key = "#userId",
        lockStrategy = LockStrategyType.COMPLETABLE_FUTURE,
        l1ExpireSeconds = 300,   // 5分钟本地缓存
        l2ExpireSeconds = 1800   // 30分钟分布式缓存
    )
    public UserStats getUserStats(Long userId) {
        log.info("计算用户统计信息: {}", userId);
        // 模拟复杂的统计计算
        try {
            Thread.sleep(100); // 模拟耗时操作
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        return new UserStats(userId, 100, 50, 200);
    }

    /**
     * 普通业务数据 - 使用全局配置的策略
     * <p>
     * 适用场景：
     * - 一般的业务数据
     * - 不需要特殊优化的场景
     * <p>
     * 优势：使用全局配置，便于统一管理
     */
    @TlCache(
        cacheName = "normalData",
        key = "#dataId",
        // 不指定lockStrategy，默认使用AUTO（全局配置）
        l1ExpireSeconds = 300,
        l2ExpireSeconds = 1800
    )
    public BusinessData getNormalData(Long dataId) {
        log.info("从数据库加载普通业务数据: {}", dataId);
        // 模拟数据库查询
        return new BusinessData(dataId, "业务数据" + dataId, "normal");
    }

    /**
     * 批量数据处理 - 根据数据类型选择不同策略
     */
    @TlCache(
        cacheName = "batchData",
        key = "#type + ':' + #id",
        lockStrategy = LockStrategyType.STRIPED, // 批量处理适合用分段锁
        l1ExpireSeconds = 600,
        l2ExpireSeconds = 3600
    )
    public BatchData getBatchData(String type, Long id) {
        log.info("处理批量数据: type={}, id={}", type, id);
        // 模拟批量数据处理
        return new BatchData(type, id, "批量数据处理结果");
    }

    // 示例数据类
    public record UserInfo(Long id, String name, String email) {
    }

    public record SystemConfig(String key, String value, Long updateTime) {
    }

    public record UserStats(Long userId, Integer loginCount, Integer orderCount, Integer points) {
    }

    public record BusinessData(Long id, String name, String type) {
    }

    public record BatchData(String type, Long id, String result) {
    }
}
