package org.dromara.common.tlcache.util;

import org.dromara.common.tlcache.annotation.BloomFilter;
import org.dromara.common.tlcache.annotation.RandomExpire;
import org.dromara.common.tlcache.config.protection.BloomFilterConfig;
import org.dromara.common.tlcache.config.protection.RandomExpireConfig;

/**
 * 防护配置转换工具类
 * <p>
 * 将注解配置转换为配置对象
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public class ProtectionConfigConverter {

    /**
     * 转换布隆过滤器注解为配置对象
     *
     * @param annotation 布隆过滤器注解
     * @return 布隆过滤器配置对象
     */
    public static BloomFilterConfig convert(BloomFilter annotation) {
        if (annotation == null || !annotation.enabled()) {
            return null;
        }

        BloomFilterConfig config = new BloomFilterConfig();
        config.setEnabled(annotation.enabled());
        config.setExpectedInsertions(annotation.expectedInsertions());
        config.setFpp(annotation.fpp());
        config.setFilterName(annotation.filterName().isEmpty() ? null : annotation.filterName());
        config.setAutoInit(annotation.autoInit());
        config.setExpireSeconds(annotation.expireSeconds());

        // 验证配置
        config.validate();
        return config;
    }

    /**
     * 转换随机过期注解为配置对象
     *
     * @param annotation 随机过期注解
     * @return 随机过期配置对象
     */
    public static RandomExpireConfig convert(RandomExpire annotation) {
        if (annotation == null || !annotation.enabled()) {
            return null;
        }

        RandomExpireConfig config = new RandomExpireConfig();
        config.setEnabled(annotation.enabled());
        config.setMinSeconds(annotation.minSeconds());
        config.setMaxSeconds(annotation.maxSeconds());
        config.setStrategy(annotation.strategy());
        config.setApplyToL1(annotation.applyToL1());

        if (annotation.randomSeed() != -1) {
            config.setRandomSeed(annotation.randomSeed());
        }

        // 验证配置
        config.validate();
        return config;
    }

    /**
     * 检查是否有任何防护配置启用
     *
     * @param bloomFilter  布隆过滤器注解
     * @param randomExpire 随机过期注解
     * @return 是否有防护配置启用
     */
    public static boolean hasAnyProtectionEnabled(BloomFilter bloomFilter, RandomExpire randomExpire) {
        return (bloomFilter != null && bloomFilter.enabled()) ||
            (randomExpire != null && randomExpire.enabled());
    }

    /**
     * 创建默认的布隆过滤器配置
     *
     * @return 默认配置
     */
    public static BloomFilterConfig createDefaultBloomFilterConfig() {
        return BloomFilterConfig.defaultConfig();
    }

    /**
     * 创建默认的随机过期配置
     *
     * @return 默认配置
     */
    public static RandomExpireConfig createDefaultRandomExpireConfig() {
        return RandomExpireConfig.defaultConfig();
    }

    /**
     * 合并全局配置和注解配置
     * <p>
     * 注解配置优先级高于全局配置
     *
     * @param globalConfig     全局配置
     * @param annotationConfig 注解配置
     * @return 合并后的配置
     */
    public static BloomFilterConfig merge(BloomFilterConfig globalConfig, BloomFilterConfig annotationConfig) {
        if (annotationConfig != null) {
            return annotationConfig;
        }
        return globalConfig;
    }

    /**
     * 合并全局配置和注解配置
     *
     * @param globalConfig     全局配置
     * @param annotationConfig 注解配置
     * @return 合并后的配置
     */
    public static RandomExpireConfig merge(RandomExpireConfig globalConfig, RandomExpireConfig annotationConfig) {
        if (annotationConfig != null) {
            return annotationConfig;
        }
        return globalConfig;
    }
}
