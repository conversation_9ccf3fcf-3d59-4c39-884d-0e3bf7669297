# 二级缓存（Caffeine + Redis）设计方案 - 高并发优化版

## 1. 设计目标

-   **提升性能**: 结合Caffeine的本地高速访问和Redis的分布式缓存能力，大幅降低数据访问延迟。
-   **降低后端负载**: 有效减少对数据库和Redis的直接访问次数，保护后端服务。
-   **易于使用**: 通过自定义注解，以非侵入式的方式为业务代码提供二级缓存能力。
-   **高并发健壮性**: 解决缓存击穿、穿透问题，并**优化分布式锁竞争机制**，提升高并发下的系统吞吐量。
-   **简化设计**: 移除缓存同步和主动失效机制，依赖TTL（存活时间）实现最终一致性。

## 2. 整体架构

### 2.1 架构图

```mermaid
graph TD
    subgraph "应用实例 (JVM)"
        A1["业务请求"] --> A2{"@TwoLevelCache 切面"};
        A2 -- "缓存命中" --> A4["返回响应"];
        A2 -- "缓存未命中" --> A5{"本地加载缓存 (CompletableFuture)"};
        A5 -- "跟随者线程" --> A5_WAIT["内存中等待结果"];
        A5_WAIT --> A4;
        A5 -- "领导者线程" --> A_REDIS_LOCK{"获取Redis分布式锁"};
        A_REDIS_LOCK -- "获取成功" --> A8["查询DB/回填缓存"];
        A8 --> A5_WAIT;
         A_REDIS_LOCK -- "获取失败" --> A_RETRY["短暂等待后重试"];
         A_RETRY --> A2
    end
```

### 2.2 分层说明

1.  **注解层 (`@TwoLevelCache`)**:
    -   **职责**: 作为缓存功能的声明式入口。

2.  **切面层 (AOP Aspect)**:
    -   **职责**: 拦截被注解标记的方法，实现缓存的读、写核心调度逻辑。是本次**高并发优化**的核心。

3.  **缓存管理层 (`TwoLevelCacheManager`)**:
    -   **职责**: 封装对Caffeine和Redis的底层操作。

4.  **本地加载缓存 (In-JVM Loading Cache)**:
    -   **职责**: 本次优化的核心。使用 `ConcurrentHashMap<String, CompletableFuture<Object>>` 管理正在加载的数据。在单个JVM内，对于同一个key，只有一个线程（领导者）会去加载数据，其他线程（跟随者）则异步等待结果，从而避免了大量线程去竞争分布式锁。

---

## 3. 核心实现

### 3.1. 优化方案：本地锁 + CompletableFuture

在高并发下，如果一个热点key的L1和L2缓存同时失效，让所有请求线程都去竞争同一个Redis分布式锁，会导致大量线程阻塞，严重影响性能。

**优化思路**：引入一个JVM内的“加载中”缓存，使用 `CompletableFuture` 异步协作机制。

-   **领导者线程**: 当缓存未命中时，第一个到达的线程成为“领导者”。它负责创建一个 `CompletableFuture` 放入“加载中”缓存，然后去竞争分布式锁，并执行后续的数据加载逻辑。
-   **跟随者线程**: 后续到达的线程发现“加载中”缓存已有对应的 `CompletableFuture`，它们就成为“跟随者”，不再竞争分布式锁，而是直接在内存中等待该Future的结果。

**效果**: 锁竞争从“集群总线程数”降低到“应用实例数”，极大减轻了Redis压力，并消除了线程空转。

**时序图**:
```mermaid
sequenceDiagram
    participant T1 as Thread A (Leader)
    participant T2 as Thread B (Follower)
    participant LoadingCache as In-JVM Loading Cache
    participant RedisLock as Redis Distributed Lock
    participant DB as Database

    T1->>LoadingCache: Request key 'X', miss L1/L2
    T1->>LoadingCache: putIfAbsent('X', new CompletableFuture()) -> SUCCESS
    T1->>RedisLock: Acquire distributed lock for 'X' -> SUCCESS
    T1->>DB: SELECT * FROM ...
    
    T2->>LoadingCache: Request key 'X', miss L1/L2
    T2->>LoadingCache: putIfAbsent('X', ...) -> FAIL (Future already exists)
    T2->>LoadingCache: Get existing CompletableFuture for 'X'
    T2->>T1: Waits on Future.get() (in-memory wait)

    DB-->>T1: Return result
    T1->>T1: future.complete(result)
    T1->>LoadingCache: Remove Future for 'X'
    T1->>RedisLock: Release distributed lock for 'X'

    Note right of T2: Receives result from Future,<br/>immediately returns.
```

### 3.2. 自定义注解

`@TwoLevelCache`

```java
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface TwoLevelCache {

    /**
     * 缓存名称，用于隔离不同业务的缓存。
     * 必填。
     */
    String cacheName();

    /**
     * SpEL表达式，用于从方法参数中动态生成缓存的key。
     * 例如：'#id' 或 '#user.id'。
     * 必填。
     */
    String key();

    /**
     * L1 Caffeine缓存的过期时间，单位为秒。
     * 默认60秒。
     */
    long l1ExpireSeconds() default 60;

    /**
     * L2 Redis缓存的过期时间，单位为秒。
     * 默认300秒。
     */
    long l2ExpireSeconds() default 300;
}
```

### 3.3. AOP切面 (`TwoLevelCacheAspect`) - 高并发优化版

```java
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class TwoLevelCacheAspect {

    private final TwoLevelCacheManager cacheManager;
    private final RedisLock redisLock;
    private final SpelExpressionParser parser = new SpelExpressionParser();
    private final DefaultParameterNameDiscoverer discoverer = new DefaultParameterNameDiscoverer();
    
    // JVM本地加载缓存，防止同一JVM内多线程重复加载
    private final ConcurrentHashMap<String, CompletableFuture<Object>> loadingFutures = new ConcurrentHashMap<>();
    
    private static final Object NULL_VALUE = new Object();

    @Around("@annotation(twoLevelCache)")
    public Object around(ProceedingJoinPoint joinPoint, TwoLevelCache twoLevelCache) throws Throwable {
        String cacheName = twoLevelCache.cacheName();
        String key = parseKey(twoLevelCache.key(), joinPoint);
        
        // 1. 从L1获取
        Object cache = cacheManager.getL1(cacheName, key);
        if (cache != null) {
            log.debug("L1 Cache Hit. cacheName: {}, key: {}", cacheName, key);
            return unwrapCacheValue(cache);
        }

        // 2. 从L2获取
        cache = cacheManager.getL2(cacheName, key);
        if (cache != null) {
            log.debug("L2 Cache Hit. cacheName: {}, key: {}", cacheName, key);
            cacheManager.putL1(cacheName, key, cache, twoLevelCache.l1ExpireSeconds());
            return unwrapCacheValue(cache);
        }
        
        // 3. L1/L2均未命中，使用CompletableFuture减少锁竞争
        CompletableFuture<Object> future = loadingFutures.get(key);
        if (future != null) {
            log.debug("Follower thread for key '{}'. Waiting for future to complete.", key);
            return unwrapCacheValue(future.get()); // 跟随者线程在此等待
        }

        // 领导者线程
        CompletableFuture<Object> loadingFuture = new CompletableFuture<>();
        future = loadingFutures.putIfAbsent(key, loadingFuture);
        if (future == null) { // 确认自己是领导者
            log.debug("Leader thread for key '{}'. Proceeding to load data.", key);
            try {
                Object result = loadData(joinPoint, twoLevelCache, cacheName, key);
                loadingFuture.complete(result); // 通知所有等待的线程
                return unwrapCacheValue(result);
            } catch (Throwable e) {
                loadingFuture.completeExceptionally(e); // 异常通知
                throw e;
            } finally {
                loadingFutures.remove(key); // 清理
            }
        } else { // 输掉了领导者竞赛，成为跟随者
            log.debug("Lost leader race for key '{}'. Waiting for existing future.", key);
            return unwrapCacheValue(future.get());
        }
    }

    private Object loadData(ProceedingJoinPoint joinPoint, TwoLevelCache twoLevelCache, String cacheName, String key) throws Throwable {
        String lockKey = "lock:" + cacheName + ":" + key;
        if (redisLock.tryLock(lockKey, 30, TimeUnit.SECONDS)) {
            try {
                // 双重检查
                Object result = cacheManager.getL2(cacheName, key);
                if (result != null) {
                    log.debug("Double check found cache in L2 for key '{}'.", key);
                    cacheManager.putL1(cacheName, key, result, twoLevelCache.l1ExpireSeconds());
                    return result;
                }

                // 执行方法
                Object dbResult = joinPoint.proceed();
                Object valueToCache = (dbResult == null) ? NULL_VALUE : dbResult;
                cacheManager.put(cacheName, key, valueToCache, twoLevelCache.l1ExpireSeconds(), twoLevelCache.l2ExpireSeconds());
                return valueToCache;
            } finally {
                redisLock.unlock(lockKey);
            }
        } else {
            // 未获取到分布式锁，短暂休眠后，让上层重试（通过再次调用around）
            log.warn("Failed to acquire distributed lock for key '{}'. Retrying after sleep.", key);
            Thread.sleep(100);
            // 这里不应再调用around, 而是直接从缓存再次获取，因为可能已被其他实例加载
            Object value = cacheManager.getL2(cacheName, key);
            if(value != null) {
                cacheManager.putL1(cacheName, key, value, twoLevelCache.l1ExpireSeconds());
                return value;
            }
            // 如果还是没有，可以抛出异常或返回null，避免无限递归
             log.error("Failed to load cache for key '{}' after retrying.", key);
             throw new RuntimeException("Failed to load cache data for key: " + key);
        }
    }
    
    private Object unwrapCacheValue(Object cacheValue) {
        return cacheValue == NULL_VALUE ? null : cacheValue;
    }
    
    private String parseKey(String keyExpression, JoinPoint joinPoint) {
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        String[] paramNames = discoverer.getParameterNames(method);
        Object[] args = joinPoint.getArgs();
        
        StandardEvaluationContext context = new StandardEvaluationContext();
        for (int i = 0; i < paramNames.length; i++) {
            context.setVariable(paramNames[i], args[i]);
        }
        
        return parser.parseExpression(keyExpression).getValue(context, String.class);
    }
}
```

### 3.4. 缓存管理器 (`TwoLevelCacheManager`)

```java
@Component
@RequiredArgsConstructor
@Slf4j
public class TwoLevelCacheManager {

    private final RedisTemplate<String, Object> redisTemplate;
    private final CacheConfigProperties cacheConfigProperties; // 注入配置类

    // 使用ConcurrentHashMap来存储不同cacheName对应的Caffeine实例
    private final Map<String, Cache<String, Object>> caffeineCaches = new ConcurrentHashMap<>();

    public Object getL1(String cacheName, String key) {
        Cache<String, Object> cache = caffeineCaches.get(cacheName);
        return cache != null ? cache.getIfPresent(key) : null;
    }

    public Object getL2(String cacheName, String key) {
        String redisKey = buildRedisKey(cacheName, key);
        return redisTemplate.opsForValue().get(redisKey);
    }

    public void put(String cacheName, String key, Object value, long l1Expire, long l2Expire) {
        // 先写L2
        String redisKey = buildRedisKey(cacheName, key);
        redisTemplate.opsForValue().set(redisKey, value, l2Expire, TimeUnit.SECONDS);
        
        // 再写L1
        putL1(cacheName, key, value, l1Expire);
    }
    
    public void putL1(String cacheName, String key, Object value, long l1Expire) {
        // 从配置中动态获取或使用注解中的默认值
        CacheConfigProperties.ConfigItem config = cacheConfigProperties.findConfig(cacheName);
        long expire = (l1Expire > 0) ? l1Expire : config.getL1ExpireSeconds();
        long maxSize = config.getL1MaxSize();

        Cache<String, Object> cache = caffeineCaches.computeIfAbsent(cacheName, c -> 
            Caffeine.newBuilder()
                .expireAfterWrite(expire, TimeUnit.SECONDS)
                .maximumSize(maxSize)
                .recordStats() // 开启统计功能
                .build()
        );
        cache.put(key, value);
    }

    private String buildRedisKey(String cacheName, String key) {
        // 使用配置的全局前缀，防止多应用冲突
        return cacheConfigProperties.getRedisKeyPrefix() + ":" + cacheName + ":" + key;
    }
    
    // 提供给Actuator监控端点使用
    public Map<String, Cache<String, Object>> getAllCaches() {
        return caffeineCaches;
    }
}
```

### 3.5. 配置文件 (`application.yml`)

```yaml
app:
  cache:
    # Redis Key的全局前缀，建议使用应用名，防止多应用冲突
    redis-key-prefix: "meht"
    # 各个缓存的详细配置
    configs:
      - cacheName: "user"       # 缓存名称，与@TwoLevelCache中对应
        l1-max-size: 2000       # L1缓存最大条目
        l1-expire-seconds: 60   # L1缓存过期时间（秒）
      - cacheName: "product"
        l1-max-size: 500
        l1-expire-seconds: 120
      - cacheName: "default"    # 提供一个默认配置
        l1-max-size: 1000
        l1-expire-seconds: 30
```

对应的配置绑定类 `CacheConfigProperties.java`:
```java
@Data
@Component
@ConfigurationProperties(prefix = "app.cache")
public class CacheConfigProperties {

    private String redisKeyPrefix = "cache";
    private List<ConfigItem> configs = new ArrayList<>();

    @Data
    public static class ConfigItem {
        private String cacheName;
        private long l1MaxSize = 1000;
        private long l1ExpireSeconds = 60;
    }

    public ConfigItem findConfig(String cacheName) {
        return configs.stream()
            .filter(c -> c.getCacheName().equals(cacheName))
            .findFirst()
            // 如果找不到特定名称的配置，就查找名为"default"的配置
            .orElse(configs.stream()
                .filter(c -> "default".equals(c.getCacheName()))
                .findFirst()
                // 如果连default都找不到，返回一个硬编码的默认值
                .orElse(new ConfigItem()));
    }
}
```

## 4. 使用示例

```java
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;

    @Override
    @TwoLevelCache(cacheName = "user", key = "#id")
    public User getUserById(Long id) {
        // 方法实现...
        return userMapper.selectById(id);
    }
}
```

## 5. 监控与运维

为了方便线上问题的排查和性能监控，可以创建一个自定义的Actuator端点来暴露二级缓存的状态。

### 5.1. 自定义Actuator端点 (`TwoLevelCacheEndpoint.java`)

```java
@Component
@Endpoint(id = "twolevelcache")
public class TwoLevelCacheEndpoint {

    private final TwoLevelCacheManager cacheManager;
    private final RedisTemplate<String, Object> redisTemplate;
    private final CacheConfigProperties cacheConfigProperties;

    // 查看所有L1缓存实例的名称
    @ReadOperation
    public Set<String> listCaches() {
        return cacheManager.getAllCaches().keySet();
    }

    // 查看指定L1缓存的详细统计信息和L2 Key样本
    @ReadOperation
    public Map<String, Object> cacheDetails(@Selector String cacheName) {
        Map<String, Object> details = new LinkedHashMap<>();
        Cache<String, Object> caffeineCache = cacheManager.getAllCaches().get(cacheName);

        if (caffeineCache == null) {
            return Collections.singletonMap("error", "Cache with name '" + cacheName + "' not found.");
        }

        details.put("l1_stats", caffeineCache.stats());
        details.put("l1_size", caffeineCache.estimatedSize());

        // 为了性能，只扫描少量L2的key作为样本
        try {
            String pattern = cacheConfigProperties.getRedisKeyPrefix() + ":" + cacheName + ":*";
            Set<String> redisKeys = redisTemplate.keys(pattern);
            details.put("l2_key_pattern", pattern);
            details.put("l2_key_samples", redisKeys.stream().limit(20).collect(Collectors.toList()));
        } catch (Exception e) {
            details.put("l2_keys_error", e.getMessage());
        }

        return details;
    }
}
```

### 5.2. 如何使用

1.  **GET `/actuator/twolevelcache`**: 列出所有已创建的L1缓存的名称。
2.  **GET `/actuator/twolevelcache/{cacheName}`**: 查看名为`{cacheName}`的缓存详情，包括Caffeine的命中率、大小、驱逐数等，以及Redis中对应的Key样本。

## 6. 优缺点分析

### 6.1 优点

-   **性能卓越**：充分利用了Caffeine和Redis。
-   **高并发性能优异**：通过**本地加载缓存 + CompletableFuture**机制，将分布式锁的竞争压力从“集群总线程数”降低到“应用实例数”，极大地减少了线程阻塞和Redis压力，显著提升了高并发下的系统吞吐量。
-   **代码解耦**：业务代码与缓存逻辑完全分离。
-   **健壮性高**：内置了缓存穿透、击穿的解决方案。
-   **实现简单**：相较于带同步机制的版本，实现和维护成本更低。

### 6.2 缺点

-   **数据一致性较弱**：数据更新依赖缓存的TTL过期策略，存在数据不一致的窗口期。
-   **内存占用**：一级缓存和加载中的Future都会占用应用自身的堆内存，需要合理配置。
-   **复杂度提升**：相较于基础版本，引入了 `CompletableFuture` 和更复杂的并发控制逻辑，对开发人员的理解要求更高。 