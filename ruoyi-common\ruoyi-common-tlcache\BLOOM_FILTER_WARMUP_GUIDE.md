# 🌸 布隆过滤器预热功能使用指南

## 📋 概述

布隆过滤器预热功能提供了一个轻量级的配置化方案，用于在应用启动时自动初始化布隆过滤器。通过简单的YAML配置，您可以轻松地为不同的业务场景配置布隆过滤器预热策略。

## 🎯 功能特点

- **配置化管理**：通过YAML配置文件管理，无需编写代码
- **轻量级设计**：只需3个核心类，代码量少，易于维护
- **多过滤器支持**：支持配置多个布隆过滤器的预热策略
- **SQL驱动**：通过SQL查询获取预热数据，灵活性高
- **自动集成**：与现有的@TlCache注解无缝集成

## 🚀 快速开始

### 1. 启用功能

在 `application.yml` 中添加配置：

```yaml
tl-cache:
  bloom-filter-warmup:
    enabled: true
    configs:
      - name: "user"
        description: "用户ID布隆过滤器预热"
        sql: "SELECT user_id FROM sys_user WHERE del_flag = '0'"
        filter-config:
          expected-insertions: 500000
          fpp: 0.005
          filter-name: "user"
```

### 2. 使用缓存注解

在Service方法上使用@TlCache注解：

```java
@TlCache(
    cacheName = "user",
    key = "#userId",
    bloomFilter = @BloomFilter(
        enabled = true,
        filterName = "user",           // 对应配置中的filter-name
        expectedInsertions = 500000,   // 与配置保持一致
        fpp = 0.005                    // 与配置保持一致
    )
)
public UserInfo getUserInfo(Long userId) {
    return userMapper.selectById(userId);
}
```

### 3. 启动应用

应用启动时会自动执行预热，日志输出示例：

```
2025-07-22 10:00:00.123 INFO  --- 开始预热布隆过滤器，共1个配置
2025-07-22 10:00:00.456 INFO  --- 开始预热布隆过滤器: user - 用户ID布隆过滤器预热
2025-07-22 10:00:01.789 INFO  --- 布隆过滤器 user 预热完成，查询数据: 12345, 成功添加: 12345
2025-07-22 10:00:01.790 INFO  --- 布隆过滤器预热完成，成功: 1, 失败: 0
```

## ⚙️ 配置详解

### 完整配置示例

```yaml
tl-cache:
  bloom-filter-warmup:
    enabled: true
    configs:
      # 用户布隆过滤器
      - name: "user"
        description: "用户ID布隆过滤器预热"
        sql: "SELECT user_id FROM sys_user WHERE del_flag = '0'"
        filter-config:
          expected-insertions: 500000    # 预期插入数量
          fpp: 0.005                     # 误判率
          filter-name: "user"            # 过滤器名称
          auto-init: true                # 自动初始化
          expire-seconds: -1             # 过期时间（-1永不过期）
          
      # 角色布隆过滤器
      - name: "role"
        description: "角色ID布隆过滤器预热"
        sql: "SELECT role_id FROM sys_role WHERE del_flag = '0'"
        filter-config:
          expected-insertions: 10000
          fpp: 0.01
          filter-name: "role"
```

### 配置参数说明

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| `enabled` | boolean | 否 | false | 是否启用预热功能 |
| `name` | string | 是 | - | 配置名称，必须唯一 |
| `description` | string | 否 | - | 配置描述，用于日志 |
| `sql` | string | 是 | - | SQL查询语句 |
| `expected-insertions` | long | 否 | 1000000 | 预期插入数量 |
| `fpp` | double | 否 | 0.01 | 误判率（0-1之间） |
| `filter-name` | string | 否 | name值 | 布隆过滤器名称 |
| `auto-init` | boolean | 否 | true | 是否自动初始化 |
| `expire-seconds` | long | 否 | -1 | 过期时间（秒） |

## 📝 使用示例

### 示例1：系统用户预热

```yaml
configs:
  - name: "user"
    description: "系统用户布隆过滤器预热"
    sql: "SELECT user_id FROM sys_user WHERE del_flag = '0' AND status = '0'"
    filter-config:
      expected-insertions: 500000
      fpp: 0.005
      filter-name: "user"
```

对应的Service方法：

```java
@TlCache(
    cacheName = "user",
    key = "#userId",
    bloomFilter = @BloomFilter(
        enabled = true,
        filterName = "user",
        expectedInsertions = 500000,
        fpp = 0.005
    )
)
public SysUser getUserById(Long userId) {
    return userMapper.selectById(userId);
}
```

### 示例2：商品信息预热

```yaml
configs:
  - name: "product"
    description: "商品信息布隆过滤器预热"
    sql: "SELECT product_id FROM product WHERE status = 1 AND del_flag = '0'"
    filter-config:
      expected-insertions: 1000000
      fpp: 0.01
      filter-name: "product"
```

### 示例3：多表联合查询

```yaml
configs:
  - name: "user_role"
    description: "用户角色关联布隆过滤器预热"
    sql: "SELECT CONCAT(user_id, ':', role_id) FROM sys_user_role WHERE del_flag = '0'"
    filter-config:
      expected-insertions: 2000000
      fpp: 0.01
      filter-name: "user_role"
```

## ⚠️ 注意事项

### 1. SQL查询要求
- SQL查询结果的第一列将作为布隆过滤器的key
- 查询结果必须是字符串类型或可转换为字符串的类型
- 建议在SQL中添加适当的WHERE条件和索引以提高查询性能

### 2. 参数一致性
- @BloomFilter注解中的参数必须与配置文件中的参数保持一致
- 特别是`expectedInsertions`和`fpp`参数，不一致可能导致性能问题

### 3. 性能考虑
- 对于大表查询，预热可能会影响应用启动时间
- 建议根据实际数据量合理设置`expected-insertions`参数
- 可以通过添加WHERE条件限制预热的数据范围

### 4. 内存使用
- 布隆过滤器会占用一定的Redis内存
- `fpp`越小，占用内存越大
- 建议根据业务需求平衡误判率和内存使用

## 🔧 故障排除

### 常见问题

1. **预热失败**
   - 检查SQL语句是否正确
   - 确认数据库连接是否正常
   - 查看应用日志中的错误信息

2. **布隆过滤器不生效**
   - 确认配置文件中的`enabled`为true
   - 检查@BloomFilter注解中的`filterName`是否与配置一致
   - 验证Redis连接是否正常

3. **启动时间过长**
   - 优化SQL查询，添加索引
   - 减少预热数据量
   - 考虑异步预热（后续版本支持）

### 日志级别调整

如需查看详细的预热过程，可以调整日志级别：

```yaml
logging:
  level:
    org.dromara.common.tlcache.runner: DEBUG
```

## 🎉 总结

布隆过滤器预热功能提供了一个简单而强大的配置化方案，让您可以轻松地为不同的业务场景配置布隆过滤器防穿透机制。通过合理的配置和使用，可以有效提升系统的性能和稳定性。
