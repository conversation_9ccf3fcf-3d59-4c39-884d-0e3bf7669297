package org.dromara.common.tlcache.manager;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.tlcache.config.properties.TlCacheConfigProperties;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 二级缓存管理器
 * 管理L1(Caffeine)和L2(Redis)缓存
 * <p>
 * 该类负责管理二级缓存系统的核心操作：
 * - L1缓存：基于Caffeine的本地内存缓存，访问速度快但仅在当前JVM有效
 * - L2缓存：基于Redis的分布式缓存，可在多个应用实例间共享
 * <p>
 * 提供缓存的读取、写入等基本操作，并处理L1和L2缓存之间的数据同步
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Component
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(prefix = "tl-cache", name = "enabled", havingValue = "true", matchIfMissing = true)
public class TlCacheManager {
    private final RedissonClient redissonClient;
    private final TlCacheConfigProperties cacheProperties;

    /**
     * L1缓存映射表 (cacheName -> Cache)
     * <p>
     * 存储不同业务域的L1缓存实例
     * 按cacheName分组，每个业务域使用独立的Caffeine缓存实例
     */
    private final Map<String, Cache<String, Object>> l1CacheMap = new ConcurrentHashMap<>();


    /**
     * 获取或创建L1缓存
     * <p>
     * 根据缓存名称获取现有的L1缓存实例，如果不存在则创建新的实例
     * 新创建的缓存实例会根据配置设置最大容量和过期时间
     * 如果L1缓存被禁用，则返回null
     *
     * @param cacheName       缓存名称
     * @param l1ExpireSeconds L1缓存过期时间（秒）
     * @return Caffeine缓存实例，如果L1缓存被禁用则返回null
     */
    public Cache<String, Object> getOrCreateL1Cache(String cacheName, long l1ExpireSeconds) {
        if (!cacheProperties.isEnableL1Cache()) {
            return null;
        }

        return l1CacheMap.computeIfAbsent(cacheName, name -> {
            int maxSize = cacheProperties.getL1MaxSize();
            log.debug("创建L1缓存实例: {}, 最大容量: {}, 过期时间: {}秒", name, maxSize, l1ExpireSeconds);
            return Caffeine.newBuilder()
                .maximumSize(maxSize)
                .expireAfterWrite(l1ExpireSeconds, TimeUnit.SECONDS)
                .build();
        });
    }

    /**
     * 从L1缓存获取值
     * <p>
     * 根据缓存名称和键从L1缓存获取值
     * 如果缓存不存在或已过期，则返回null
     * 如果L1缓存被禁用，则直接返回null
     *
     * @param cacheName       缓存名称
     * @param key             缓存键
     * @param l1ExpireSeconds L1缓存过期时间（秒）
     * @return 缓存值，如果不存在或L1缓存被禁用则返回null
     */
    public Object getL1(String cacheName, String key, long l1ExpireSeconds) {
        if (!cacheProperties.isEnableL1Cache()) {
            return null;
        }

        Cache<String, Object> l1Cache = getOrCreateL1Cache(cacheName, l1ExpireSeconds);
        return l1Cache != null ? l1Cache.getIfPresent(key) : null;
    }

    /**
     * 从L2缓存获取值
     * <p>
     * 根据缓存名称和键从L2缓存(Redis)获取值
     *
     * @param cacheName 缓存名称
     * @param key       缓存键
     * @return 缓存值，如果不存在则返回null
     */
    public Object getL2(String cacheName, String key) {
        String redisKey = buildRedisKey(cacheName, key);
        return redissonClient.getBucket(redisKey).get();
    }

    /**
     * 将值放入L1缓存
     * <p>
     * 根据缓存名称和键将值存入L1缓存
     * 如果指定名称的缓存不存在，则创建新的缓存实例
     * 如果L1缓存被禁用，则此方法不执行任何操作
     * <p>
     * 注意：由于Caffeine不支持动态设置单个key的过期时间，
     * 这里的expireSeconds参数用于创建新的缓存实例时设置过期时间
     *
     * @param cacheName     缓存名称
     * @param key           缓存键
     * @param value         缓存值
     * @param expireSeconds 过期时间（秒）
     */
    public void putL1(String cacheName, String key, Object value, long expireSeconds) {
        if (!cacheProperties.isEnableL1Cache()) {
            log.debug("L1缓存已禁用，跳过L1缓存写入: cacheName={}, key={}", cacheName, key);
            return;
        }

        // 检查是否需要创建新的缓存实例（如果过期时间与默认不同）
        Cache<String, Object> cache = l1CacheMap.get(cacheName);
        if (cache == null) {
            // 创建新的缓存实例，使用指定的过期时间
            cache = Caffeine.newBuilder()
                .maximumSize(cacheProperties.getL1MaxSize())
                .expireAfterWrite(expireSeconds, TimeUnit.SECONDS)
                .build();

            // 尝试放入，如果已存在则使用现有的
            Cache<String, Object> existingCache = l1CacheMap.putIfAbsent(cacheName, cache);
            if (existingCache != null) {
                // 如果已存在，使用现有的缓存实例
                cache = existingCache;
            } else {
                log.debug("为cacheName={}创建了新的L1缓存，过期时间={}秒", cacheName, expireSeconds);
            }
        }

        // 将值放入缓存
        cache.put(key, value);
    }

    /**
     * 将值放入L2缓存
     * <p>
     * 根据缓存名称和键将值存入L2缓存(Redis)
     * 并设置过期时间
     *
     * @param cacheName     缓存名称
     * @param key           缓存键
     * @param value         缓存值
     * @param expireSeconds 过期时间（秒）
     */
    public void putL2(String cacheName, String key, Object value, long expireSeconds) {
        String redisKey = buildRedisKey(cacheName, key);
        RBucket<Object> bucket = redissonClient.getBucket(redisKey);
        bucket.set(value, expireSeconds, TimeUnit.SECONDS);
    }

    /**
     * 将值放入缓存（L1和L2）
     * <p>
     * 同时将值存入L1和L2缓存
     * 可以为L1和L2缓存设置不同的过期时间
     *
     * @param cacheName       缓存名称
     * @param key             缓存键
     * @param value           缓存值
     * @param l1ExpireSeconds L1缓存过期时间（秒）
     * @param l2ExpireSeconds L2缓存过期时间（秒）
     */
    public void put(String cacheName, String key, Object value, long l1ExpireSeconds, long l2ExpireSeconds) {
        // L1缓存
        putL1(cacheName, key, value, l1ExpireSeconds);

        // L2缓存
        putL2(cacheName, key, value, l2ExpireSeconds);
    }


    /**
     * 构建Redis键
     * <p>
     * 根据缓存名称和键构建完整的Redis键
     * 格式为：{redisKeyPrefix}:{cacheName}:{key}
     *
     * @param cacheName 缓存名称
     * @param key       缓存键
     * @return 完整的Redis键
     */
    private String buildRedisKey(String cacheName, String key) {
        return cacheProperties.getRedisKeyPrefix() + ":" + cacheName + ":" + key;
    }
}
