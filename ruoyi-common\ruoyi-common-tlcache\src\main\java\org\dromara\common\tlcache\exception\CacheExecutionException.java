package org.dromara.common.tlcache.exception;

import lombok.Getter;

/**
 * 缓存方法执行异常
 * <p>
 * 当被缓存的方法执行过程中发生异常时抛出此异常
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Getter
public class CacheExecutionException extends TlCacheException {

    /**
     * 方法名称
     * -- GETTER --
     * 获取方法名称
     *
     * @return 方法名称
     */
    private final String methodName;

    /**
     * 缓存名称
     * -- GETTER --
     * 获取缓存名称
     *
     * @return 缓存名称
     */
    private final String cacheName;

    /**
     * 缓存键
     * -- GETTER --
     * 获取缓存键
     *
     * @return 缓存键
     */
    private final String cacheKey;

    /**
     * 构造函数
     *
     * @param methodName 方法名称
     * @param cacheName  缓存名称
     * @param cacheKey   缓存键
     * @param message    异常信息
     * @param cause      原始异常
     */
    public CacheExecutionException(String methodName, String cacheName, String cacheKey, String message, Throwable cause) {
        super("CACHE_EXECUTION_ERROR",
            String.format("缓存方法执行失败: method=%s, cacheName=%s, key=%s, reason=%s",
                methodName, cacheName, cacheKey, message), cause);
        this.methodName = methodName;
        this.cacheName = cacheName;
        this.cacheKey = cacheKey;
    }

}
