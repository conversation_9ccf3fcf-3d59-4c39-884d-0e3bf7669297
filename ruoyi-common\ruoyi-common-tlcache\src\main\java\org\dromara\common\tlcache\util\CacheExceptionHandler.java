package org.dromara.common.tlcache.util;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.tlcache.exception.CacheExecutionException;

import java.util.function.Supplier;

/**
 * 缓存异常处理工具类
 * <p>
 * 提供统一的异常处理逻辑，避免代码重复
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Slf4j
public final class CacheExceptionHandler {

    private CacheExceptionHandler() {
        // 工具类，禁止实例化
    }

    /**
     * 执行缓存方法并处理异常
     * <p>
     * 统一处理缓存方法执行过程中的异常，保持异常链
     *
     * @param methodName 方法名称
     * @param cacheName  缓存名称
     * @param cacheKey   缓存键
     * @param supplier   方法执行逻辑
     * @param <T>        返回值类型
     * @return 方法执行结果
     * @throws CacheExecutionException 缓存执行异常
     */
    public static <T> T executeWithExceptionHandling(String methodName, String cacheName, String cacheKey,
                                                     Supplier<T> supplier) {
        try {
            return supplier.get();
        } catch (Throwable e) {
            log.error("缓存方法执行失败: method={}, cacheName={}, key={}", methodName, cacheName, cacheKey, e);

            if (e instanceof RuntimeException) {
                throw new CacheExecutionException(methodName, cacheName, cacheKey, e.getMessage(), e);
            }
            throw new CacheExecutionException(methodName, cacheName, cacheKey, "方法执行异常", e);
        }
    }

    /**
     * 执行缓存方法并处理异常（带返回值检查）
     * <p>
     * 在异常处理基础上，增加返回值的合法性检查
     *
     * @param methodName    方法名称
     * @param cacheName     缓存名称
     * @param cacheKey      缓存键
     * @param supplier      方法执行逻辑
     * @param resultChecker 结果检查器，返回true表示结果有效
     * @param <T>           返回值类型
     * @return 方法执行结果
     * @throws CacheExecutionException 缓存执行异常
     */
    public static <T> T executeWithValidation(String methodName, String cacheName, String cacheKey,
                                              Supplier<T> supplier, java.util.function.Predicate<T> resultChecker) {
        T result = executeWithExceptionHandling(methodName, cacheName, cacheKey, supplier);

        if (result != null && !resultChecker.test(result)) {
            log.warn("缓存方法返回值校验失败: method={}, cacheName={}, key={}", methodName, cacheName, cacheKey);
        }

        return result;
    }

    /**
     * 安全执行，异常时返回默认值
     * <p>
     * 当方法执行失败时，返回指定的默认值而不抛出异常
     *
     * @param methodName   方法名称
     * @param cacheName    缓存名称
     * @param cacheKey     缓存键
     * @param supplier     方法执行逻辑
     * @param defaultValue 默认值
     * @param <T>          返回值类型
     * @return 方法执行结果或默认值
     */
    public static <T> T executeSafely(String methodName, String cacheName, String cacheKey,
                                      Supplier<T> supplier, T defaultValue) {
        try {
            return executeWithExceptionHandling(methodName, cacheName, cacheKey, supplier);
        } catch (CacheExecutionException e) {
            log.warn("缓存方法执行失败，返回默认值: method={}, cacheName={}, key={}, error={}",
                methodName, cacheName, cacheKey, e.getMessage());
            return defaultValue;
        }
    }
}
