package org.dromara.common.tlcache.enums;

/**
 * 锁策略类型枚举
 * <p>
 * 定义二级缓存系统支持的锁策略类型，用于在不同并发场景下选择最优的锁实现。
 * 每种策略都有其适用的场景和性能特点：
 * <ul>
 * <li>AUTO: 使用全局配置的锁策略，默认选项，便于统一管理</li>
 * <li>SYNCHRONIZED: 基于synchronized和Redisson的锁策略，适用于一般并发场景，实现简单可靠</li>
 * <li>COMPLETABLE_FUTURE: 基于CompletableFuture的锁策略，适用于高并发场景，减少分布式锁竞争</li>
 * <li>STRIPED: 基于分段锁的策略，减少锁竞争，适用于高并发热点数据场景，性能最佳</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public enum LockStrategyType {

    /**
     * 自动选择，使用全局配置的锁策略
     * <p>
     * 适用场景：
     * <ul>
     * <li>大部分普通业务数据</li>
     * <li>不需要特殊优化的场景</li>
     * <li>便于统一管理和配置</li>
     * </ul>
     * <p>
     * 优势：
     * <ul>
     * <li>使用全局配置，便于统一管理</li>
     * <li>可以通过修改配置文件统一调整策略</li>
     * <li>适合渐进式优化</li>
     * </ul>
     */
    AUTO,

    /**
     * 同步锁策略，使用本地synchronized锁和Redisson分布式锁
     * <p>
     * 适用场景：
     * <ul>
     * <li>重要的配置数据</li>
     * <li>需要强一致性保证的数据</li>
     * <li>低频但重要的业务数据</li>
     * <li>系统参数和业务规则</li>
     * </ul>
     * <p>
     * 特点：
     * <ul>
     * <li>实现简单，易于理解和维护</li>
     * <li>保证强一致性</li>
     * <li>适合并发度不高的场景</li>
     * </ul>
     */
    SYNCHRONIZED,

    /**
     * CompletableFuture锁策略，使用CompletableFuture和Redisson分布式锁
     * <p>
     * 适用场景：
     * <ul>
     * <li>统计类数据和报表数据</li>
     * <li>可以容忍短暂不一致的数据</li>
     * <li>高并发但对一致性要求不严格的场景</li>
     * <li>分析和计算类数据</li>
     * </ul>
     * <p>
     * 特点：
     * <ul>
     * <li>使用"领导者-跟随者"模式减少分布式锁竞争</li>
     * <li>适合高并发场景</li>
     * <li>可能存在短暂的数据不一致</li>
     * </ul>
     */
    COMPLETABLE_FUTURE,

    /**
     * 分段锁策略，使用Guava的Striped锁减少锁竞争
     * <p>
     * 适用场景：
     * <ul>
     * <li>高频访问的热点数据</li>
     * <li>用户信息、商品详情等核心业务数据</li>
     * <li>高并发场景下的缓存操作</li>
     * <li>需要最佳性能的关键路径</li>
     * </ul>
     * <p>
     * 特点：
     * <ul>
     * <li>使用分段锁技术，显著减少锁竞争</li>
     * <li>在高并发场景下性能最佳</li>
     * <li>保证数据一致性</li>
     * <li>适合热点数据的缓存场景</li>
     * </ul>
     */
    STRIPED;

    /**
     * 获取策略的描述信息
     *
     * @return 策略描述
     */
    public String getDescription() {
        return switch (this) {
            case AUTO -> "自动选择，使用全局配置的锁策略";
            case SYNCHRONIZED -> "同步锁策略，适用于一般并发场景，保证强一致性";
            case COMPLETABLE_FUTURE -> "CompletableFuture策略，适用于高并发场景，减少锁竞争";
            case STRIPED -> "分段锁策略，适用于高并发热点数据，性能最佳";
        };
    }

    /**
     * 获取策略的性能等级（1-3，3为最高）
     *
     * @return 性能等级
     */
    public int getPerformanceLevel() {
        return switch (this) {
            case AUTO -> 0; // 取决于全局配置
            case SYNCHRONIZED -> 1;
            case COMPLETABLE_FUTURE -> 2;
            case STRIPED -> 3;
        };
    }

    /**
     * 获取策略的一致性等级（1-3，3为最高）
     *
     * @return 一致性等级
     */
    public int getConsistencyLevel() {
        return switch (this) {
            case AUTO -> 0; // 取决于全局配置
            case SYNCHRONIZED -> 3;
            case COMPLETABLE_FUTURE -> 2;
            case STRIPED -> 3;
        };
    }

    /**
     * 判断是否适合高并发场景
     *
     * @return 是否适合高并发
     */
    public boolean isHighConcurrencyFriendly() {
        return this == STRIPED || this == COMPLETABLE_FUTURE;
    }

    /**
     * 判断是否保证强一致性
     *
     * @return 是否保证强一致性
     */
    public boolean isStrongConsistency() {
        return this == SYNCHRONIZED || this == STRIPED;
    }
}
