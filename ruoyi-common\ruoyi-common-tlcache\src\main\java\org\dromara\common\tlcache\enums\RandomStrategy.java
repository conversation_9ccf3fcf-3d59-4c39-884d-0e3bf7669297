package org.dromara.common.tlcache.enums;

/**
 * 随机策略枚举
 * <p>
 * 用于配置随机过期时间的生成策略
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public enum RandomStrategy {

    /**
     * 均匀分布：在[minSeconds, maxSeconds]范围内均匀随机
     * <p>
     * 特点：每个值被选中的概率相等
     * 适用场景：大多数通用场景，分布均匀
     */
    UNIFORM,

    /**
     * 正态分布：以(minSeconds + maxSeconds)/2为中心的正态分布
     * <p>
     * 特点：中心值附近的概率较高，两端概率较低
     * 适用场景：希望大部分值集中在中间范围的场景
     */
    NORMAL,

    /**
     * 指数分布：偏向较小的随机值
     * <p>
     * 特点：较小的值被选中的概率更高
     * 适用场景：希望大部分随机时间较短的场景
     */
    EXPONENTIAL
}
