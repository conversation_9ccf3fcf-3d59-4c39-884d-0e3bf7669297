---
type: "agent_requested"
---

# 文档规范

## 通用要求
- 所有文档使用Markdown格式
- 使用简洁、清晰的语言
- 文档内容应保持最新
- 避免拼写和语法错误
- 使用中文作为主要语言

## 目录结构
- `README.md`：项目根目录，提供项目概述
- `docs/`：存放详细文档
  - `guide/`：使用指南
  - `api/`：API文档
  - `examples/`：示例代码文档

## README.md 内容规范
- 项目名称和简短描述
- 技术栈说明
- 项目结构说明
- 使用说明
- 许可证信息

## 版本记录规范
- 使用 `CHANGELOG.md` 记录版本变更
- 遵循语义化版本（Semantic Versioning）规范
- 每个版本应包含：新增功能、修复问题、破坏性变更

## 文档内容组织
- 从整体到局部，从简单到复杂
- 重要信息放在前面
- 相关内容应当放在一起
- 使用小标题和列表增强可读性
- 避免过长段落，保持内容简洁

## 代码示例规范
- 提供完整可运行的示例
- 代码应当简洁且易于理解
- 添加适当的注释解释关键部分
- 说明代码的预期输出或行为
- 更新示例以匹配最新API






